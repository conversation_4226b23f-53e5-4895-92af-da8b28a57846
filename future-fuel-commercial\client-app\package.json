{"name": "future-fuel-client", "productName": "مؤسسة وقود المستقبل - العميل", "version": "3.0.0", "description": "تطبيق العميل المحمي لنظام إدارة مؤسسات الوقود", "main": "main.js", "author": {"name": "مؤسسة وقود المستقبل", "email": "<EMAIL>", "url": "https://futurefuel.sa"}, "license": "Commercial", "private": true, "homepage": "https://futurefuel.sa", "repository": {"type": "git", "url": "https://github.com/futurefuel/client-app.git"}, "keywords": ["fuel", "gas-station", "management", "electron", "arabic", "commercial"], "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "publish": "electron-builder --publish=always", "postinstall": "electron-builder install-app-deps", "clean": "<PERSON><PERSON><PERSON> dist build", "lint": "eslint .", "test": "jest"}, "dependencies": {"electron-updater": "^6.1.7", "electron-log": "^5.0.1", "node-machine-id": "^1.1.12", "crypto-js": "^4.2.0", "axios": "^1.6.2"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "rimraf": "^5.0.5", "eslint": "^8.56.0", "jest": "^29.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "build": {"appId": "sa.futurefuel.client", "productName": "مؤسسة وقود المستقبل", "copyright": "Copyright © 2024 مؤسسة وقود المستقبل. جميع الحقوق محفوظة.", "directories": {"output": "dist", "buildResources": "build"}, "files": ["main.js", "preload.js", "src/**/*", "assets/**/*", "security/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "../shared", "to": "shared", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icons/app-icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}", "publisherName": "مؤسسة وقود المستقبل"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "assets/icons/installer.ico", "uninstallerIcon": "assets/icons/uninstaller.ico", "installerHeaderIcon": "assets/icons/header.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "مؤسسة وقود المستقبل", "include": "build/installer.nsh", "artifactName": "${productName}-Setup-${version}.${ext}", "deleteAppDataOnUninstall": false, "menuCategory": "Business", "runAfterFinish": true, "installerLanguages": ["ar", "en"], "language": "ar"}, "portable": {"artifactName": "${productName}-Portable-${version}.${ext}"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icons/app-icon.icns", "category": "public.app-category.business", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "dmg": {"title": "${productName} ${version}", "icon": "assets/icons/volume.icns", "background": "assets/dmg-background.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icons/app-icon.png", "category": "Office", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "publish": {"provider": "github", "owner": "futurefuel", "repo": "client-app", "private": true, "releaseType": "release"}, "compression": "maximum", "removePackageScripts": true, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "electronDownload": {"cache": ".electron-cache"}}, "electronDownload": {"mirror": "https://npm.taobao.org/mirrors/electron/"}}