/**
 * Preload Script - نص التحميل المسبق الآمن
 * Secure Preload Script for Future Fuel Client App
 * 
 * يوفر واجهة آمنة للتواصل بين العملية الرئيسية وواجهة المستخدم
 */

const { contextBridge, ipcRenderer } = require('electron');

// تعريف الواجهات الآمنة المتاحة لواجهة المستخدم
const secureAPI = {
    // وظائف المصادقة
    auth: {
        /**
         * تسجيل الدخول
         */
        login: async (credentials) => {
            try {
                return await ipcRenderer.invoke('login', credentials);
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                return {
                    success: false,
                    message: 'حدث خطأ في النظام'
                };
            }
        },

        /**
         * التحقق من الجلسة الحالية
         */
        checkSession: async () => {
            try {
                return await ipcRenderer.invoke('check-session');
            } catch (error) {
                console.error('خطأ في التحقق من الجلسة:', error);
                return { isValid: false };
            }
        },

        /**
         * تسجيل الخروج
         */
        logout: async () => {
            try {
                return await ipcRenderer.invoke('logout');
            } catch (error) {
                console.error('خطأ في تسجيل الخروج:', error);
                return { success: false };
            }
        }
    },

    // وظائف البيانات
    data: {
        /**
         * حفظ البيانات
         */
        save: async (data) => {
            try {
                return await ipcRenderer.invoke('save-data', data);
            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
                return { success: false };
            }
        },

        /**
         * تحميل البيانات
         */
        load: async (type) => {
            try {
                return await ipcRenderer.invoke('load-data', type);
            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                return { success: false, data: null };
            }
        },

        /**
         * حذف البيانات
         */
        delete: async (type, id) => {
            try {
                return await ipcRenderer.invoke('delete-data', { type, id });
            } catch (error) {
                console.error('خطأ في حذف البيانات:', error);
                return { success: false };
            }
        }
    },

    // وظائف التراخيص
    license: {
        /**
         * التحقق من الترخيص
         */
        verify: async (licenseKey) => {
            try {
                return await ipcRenderer.invoke('verify-license', licenseKey);
            } catch (error) {
                console.error('خطأ في التحقق من الترخيص:', error);
                return { isValid: false };
            }
        },

        /**
         * طلب تفعيل جديد
         */
        requestActivation: async (userInfo) => {
            try {
                return await ipcRenderer.invoke('request-activation', userInfo);
            } catch (error) {
                console.error('خطأ في طلب التفعيل:', error);
                return { success: false };
            }
        },

        /**
         * التحقق من حالة طلب التفعيل
         */
        checkActivationStatus: async (requestId) => {
            try {
                return await ipcRenderer.invoke('check-activation-status', requestId);
            } catch (error) {
                console.error('خطأ في التحقق من حالة التفعيل:', error);
                return { status: 'unknown' };
            }
        }
    },

    // وظائف النظام
    system: {
        /**
         * الحصول على معلومات النظام
         */
        getInfo: async () => {
            try {
                return await ipcRenderer.invoke('get-system-info');
            } catch (error) {
                console.error('خطأ في الحصول على معلومات النظام:', error);
                return null;
            }
        },

        /**
         * التحقق من التحديثات
         */
        checkUpdates: async () => {
            try {
                return await ipcRenderer.invoke('check-updates');
            } catch (error) {
                console.error('خطأ في التحقق من التحديثات:', error);
                return { hasUpdate: false };
            }
        },

        /**
         * إعادة تشغيل التطبيق
         */
        restart: async () => {
            try {
                return await ipcRenderer.invoke('restart-app');
            } catch (error) {
                console.error('خطأ في إعادة تشغيل التطبيق:', error);
                return { success: false };
            }
        }
    },

    // وظائف الإشعارات
    notifications: {
        /**
         * إظهار إشعار
         */
        show: (title, message, type = 'info') => {
            try {
                ipcRenderer.send('show-notification', { title, message, type });
            } catch (error) {
                console.error('خطأ في إظهار الإشعار:', error);
            }
        },

        /**
         * الاستماع للإشعارات
         */
        onReceive: (callback) => {
            try {
                ipcRenderer.on('notification-received', (event, notification) => {
                    callback(notification);
                });
            } catch (error) {
                console.error('خطأ في استقبال الإشعارات:', error);
            }
        }
    },

    // وظائف الأمان
    security: {
        /**
         * تسجيل حدث أمني
         */
        logEvent: async (event, details) => {
            try {
                return await ipcRenderer.invoke('log-security-event', { event, details });
            } catch (error) {
                console.error('خطأ في تسجيل الحدث الأمني:', error);
                return { success: false };
            }
        },

        /**
         * التحقق من سلامة التطبيق
         */
        checkIntegrity: async () => {
            try {
                return await ipcRenderer.invoke('check-app-integrity');
            } catch (error) {
                console.error('خطأ في التحقق من سلامة التطبيق:', error);
                return { isValid: false };
            }
        }
    },

    // وظائف مساعدة
    utils: {
        /**
         * تنسيق التاريخ
         */
        formatDate: (date, format = 'YYYY-MM-DD') => {
            try {
                const d = new Date(date);
                const year = d.getFullYear();
                const month = String(d.getMonth() + 1).padStart(2, '0');
                const day = String(d.getDate()).padStart(2, '0');
                
                switch (format) {
                    case 'DD/MM/YYYY':
                        return `${day}/${month}/${year}`;
                    case 'MM/DD/YYYY':
                        return `${month}/${day}/${year}`;
                    case 'YYYY-MM-DD':
                    default:
                        return `${year}-${month}-${day}`;
                }
            } catch (error) {
                console.error('خطأ في تنسيق التاريخ:', error);
                return '';
            }
        },

        /**
         * تنسيق الأرقام
         */
        formatNumber: (number, decimals = 2) => {
            try {
                return Number(number).toLocaleString('ar-SA', {
                    minimumFractionDigits: decimals,
                    maximumFractionDigits: decimals
                });
            } catch (error) {
                console.error('خطأ في تنسيق الرقم:', error);
                return '0';
            }
        },

        /**
         * تنسيق العملة
         */
        formatCurrency: (amount, currency = 'SAR') => {
            try {
                return Number(amount).toLocaleString('ar-SA', {
                    style: 'currency',
                    currency: currency,
                    minimumFractionDigits: 2
                });
            } catch (error) {
                console.error('خطأ في تنسيق العملة:', error);
                return '0 ريال';
            }
        }
    }
};

// تصدير الواجهة الآمنة لواجهة المستخدم
contextBridge.exposeInMainWorld('electronAPI', secureAPI);

// تسجيل رسالة تأكيد التحميل
console.log('✅ تم تحميل Preload Script بنجاح - واجهة آمنة متاحة');

// منع الوصول لـ Node.js APIs مباشرة
window.addEventListener('DOMContentLoaded', () => {
    // إخفاء معلومات حساسة
    delete window.process;
    delete window.require;
    delete window.exports;
    delete window.module;
    
    // إضافة معلومات التطبيق الآمنة
    window.appInfo = {
        name: 'مؤسسة وقود المستقبل',
        version: '3.0.0',
        type: 'client',
        isSecure: true
    };
    
    console.log('🔒 تم تأمين البيئة - واجهة المستخدم جاهزة');
});
