/**
 * لوحة تحكم المطور - مؤسسة وقود المستقبل (نسخة مبسطة)
 * Developer Dashboard - Future Fuel Corporation (Simplified Version)
 */

const { app, BrowserWindow, Menu } = require('electron');
const path = require('path');

// المتغيرات العامة
let mainWindow;

// التحقق من وجود نسخة واحدة فقط
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    app.on('second-instance', () => {
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });

    // إنشاء النافذة الرئيسية
    function createMainWindow() {
        mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            title: 'لوحة تحكم المطور - مؤسسة وقود المستقبل',
            icon: path.join(__dirname, 'assets', 'icons', 'icon.png'),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                webSecurity: true,
                preload: path.join(__dirname, 'src', 'preload-simple.js')
            },
            show: false,
            frame: true,
            titleBarStyle: 'default',
            backgroundColor: '#1a1a1a'
        });

        // تحميل الواجهة
        mainWindow.loadFile(path.join(__dirname, 'src', 'dashboard-simple.html'));

        // إظهار النافذة عند الانتهاء من التحميل
        mainWindow.once('ready-to-show', () => {
            mainWindow.show();
            
            // فتح أدوات المطور في بيئة التطوير
            if (process.env.NODE_ENV === 'development') {
                mainWindow.webContents.openDevTools();
            }
        });

        // إعداد القائمة
        const menuTemplate = [
            {
                label: 'ملف',
                submenu: [
                    {
                        label: 'إعادة تحميل',
                        accelerator: 'CmdOrCtrl+R',
                        click: () => {
                            mainWindow.reload();
                        }
                    },
                    {
                        label: 'أدوات المطور',
                        accelerator: 'F12',
                        click: () => {
                            mainWindow.webContents.toggleDevTools();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'خروج',
                        accelerator: 'CmdOrCtrl+Q',
                        click: () => {
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: 'عرض',
                submenu: [
                    {
                        label: 'تكبير',
                        accelerator: 'CmdOrCtrl+Plus',
                        click: () => {
                            const currentZoom = mainWindow.webContents.getZoomFactor();
                            mainWindow.webContents.setZoomFactor(currentZoom + 0.1);
                        }
                    },
                    {
                        label: 'تصغير',
                        accelerator: 'CmdOrCtrl+-',
                        click: () => {
                            const currentZoom = mainWindow.webContents.getZoomFactor();
                            mainWindow.webContents.setZoomFactor(currentZoom - 0.1);
                        }
                    },
                    {
                        label: 'إعادة تعيين التكبير',
                        accelerator: 'CmdOrCtrl+0',
                        click: () => {
                            mainWindow.webContents.setZoomFactor(1.0);
                        }
                    }
                ]
            },
            {
                label: 'مساعدة',
                submenu: [
                    {
                        label: 'حول التطبيق',
                        click: () => {
                            const { dialog } = require('electron');
                            dialog.showMessageBox(mainWindow, {
                                type: 'info',
                                title: 'حول التطبيق',
                                message: 'لوحة تحكم المطور - مؤسسة وقود المستقبل',
                                detail: 'الإصدار 3.0.0\n\nنظام إدارة التراخيص والعملاء\n\n© 2024 مؤسسة وقود المستقبل'
                            });
                        }
                    }
                ]
            }
        ];

        const menu = Menu.buildFromTemplate(menuTemplate);
        Menu.setApplicationMenu(menu);

        // معالجة إغلاق النافذة
        mainWindow.on('closed', () => {
            mainWindow = null;
        });

        // منع التنقل الخارجي
        mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
            const parsedUrl = new URL(navigationUrl);
            if (parsedUrl.origin !== 'file://') {
                event.preventDefault();
            }
        });

        // منع النوافذ الجديدة
        mainWindow.webContents.setWindowOpenHandler(({ url }) => {
            return { action: 'deny' };
        });
    }

    // تشغيل التطبيق
    app.whenReady().then(() => {
        createMainWindow();

        app.on('activate', () => {
            if (BrowserWindow.getAllWindows().length === 0) {
                createMainWindow();
            }
        });
    });

    // إغلاق التطبيق
    app.on('window-all-closed', () => {
        if (process.platform !== 'darwin') {
            app.quit();
        }
    });

    // معالجة الأخطاء
    process.on('uncaughtException', (error) => {
        console.error('خطأ غير متوقع:', error);
    });

    process.on('unhandledRejection', (reason, promise) => {
        console.error('رفض غير معالج:', reason);
    });
}

console.log('🚀 تم تشغيل لوحة تحكم المطور - مؤسسة وقود المستقبل');
