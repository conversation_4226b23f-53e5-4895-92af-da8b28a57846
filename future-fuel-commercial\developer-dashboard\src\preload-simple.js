/**
 * Preload Script - لوحة تحكم المطور (نسخة مبسطة)
 * يوفر APIs آمنة للواجهة الأمامية
 */

const { contextBridge, ipcRenderer } = require('electron');

// تعريف APIs آمنة للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
    // معلومات النظام
    platform: process.platform,
    version: process.versions.electron,
    
    // وظائف أساسية
    showMessage: (message) => {
        console.log('رسالة من التطبيق:', message);
    },
    
    // معلومات التطبيق
    getAppInfo: () => {
        return {
            name: 'لوحة تحكم المطور - مؤسسة وقود المستقبل',
            version: '3.0.0',
            description: 'نظام إدارة التراخيص والعملاء'
        };
    },
    
    // وظائف التاريخ والوقت
    getCurrentTime: () => {
        return new Date().toLocaleString('ar-SA');
    },
    
    // وظائف التخزين المحلي
    storage: {
        set: (key, value) => {
            localStorage.setItem(key, JSON.stringify(value));
        },
        get: (key) => {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        },
        remove: (key) => {
            localStorage.removeItem(key);
        },
        clear: () => {
            localStorage.clear();
        }
    },
    
    // وظائف الإشعارات
    notification: {
        show: (title, body) => {
            if (Notification.permission === 'granted') {
                new Notification(title, { body });
            }
        },
        requestPermission: async () => {
            return await Notification.requestPermission();
        }
    }
});

// تسجيل رسالة التحميل
console.log('✅ تم تحميل Preload Script بنجاح');

// معالجة الأخطاء
window.addEventListener('error', (event) => {
    console.error('خطأ في الواجهة:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('رفض غير معالج:', event.reason);
});
