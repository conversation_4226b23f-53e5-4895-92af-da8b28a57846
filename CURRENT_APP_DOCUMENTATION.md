# توثيق شامل للتطبيق الحالي - مؤسسة وقود المستقبل

## 📋 نظرة عامة
تطبيق إدارة محطات الوقود باستخدام Electron مع واجهة عربية وميزات متقدمة لإدارة العملاء والمبيعات والمخزون.

## 🗂️ هيكل المشروع

### المجلد الجذر: `resources/app/`

#### الملفات الرئيسية:
- `main.js` - ملف Electron الرئيسي
- `index.html` - الواجهة الرئيسية للتطبيق
- `package.json` - إعدادات المشروع والتبعيات
- `preload.js` - سكريبت التحميل المسبق
- `server.js` - خادم محلي للتطبيق

#### ملفات التشغيل والتثبيت:
- `start-app.bat` - تشغيل التطبيق
- `install-electron.bat` - تثبيت Electron
- `build-app.bat` - بناء التطبيق
- `create-desktop-shortcut.bat` - إنشاء اختصار سطح المكتب

### مجلد المصادر: `src/`

#### 1. نظام المصادقة: `src/auth/`
- `login.html` - واجهة تسجيل الدخول
- `login.css` - تنسيقات واجهة تسجيل الدخول
- `login.js` - منطق المصادقة والتحقق
- `license.js` - إدارة التراخيص

#### 2. نظام إدارة التراخيص: `src/license/`
- `license-dashboard.html` - لوحة تحكم التراخيص
- `license-dashboard.css` - تنسيقات لوحة التراخيص
- `license-dashboard.js` - منطق إدارة التراخيص
- `license-server.js` - خادم إدارة التراخيص
- `package.json` - تبعيات نظام التراخيص
- `data/` - مجلد بيانات التراخيص
  - `activation-requests.json` - طلبات التفعيل
  - `licenses.json` - التراخيص المُصدرة
  - `settings.json` - إعدادات النظام

#### 3. البيانات: `src/data/`
- `algeria-data.js` - بيانات الجزائر (الولايات والمدن)

#### 4. الإدارة عن بُعد: `src/remote/`
- `admin-panel.html` - لوحة الإدارة
- `admin-panel.css` - تنسيقات لوحة الإدارة
- `admin-panel.js` - منطق الإدارة عن بُعد

## 🔧 التقنيات المستخدمة

### Frontend:
- HTML5 مع دعم RTL للعربية
- CSS3 مع متغيرات CSS وتصميم متجاوب
- JavaScript ES6+ مع async/await
- Font Awesome للأيقونات
- تصميم Material Design

### Backend:
- Node.js مع Express.js
- نظام ملفات JSON للبيانات
- CORS للطلبات عبر المصادر
- Electron للتطبيق المكتبي

### الميزات الرئيسية:
- واجهة عربية كاملة مع دعم RTL
- نظام مصادقة متقدم
- إدارة التراخيص والتفعيل
- لوحة تحكم تفاعلية
- نظام إحصائيات وتقارير
- إدارة العملاء والمبيعات
- إدارة المخزون والموردين
- نظام النسخ الاحتياطي

## 📊 الوحدات الوظيفية

### 1. إدارة العملاء
- إضافة وتعديل بيانات العملاء
- تتبع تاريخ المعاملات
- إدارة بطاقات الوقود

### 2. إدارة المبيعات
- تسجيل المبيعات اليومية
- تتبع الإيرادات
- تقارير المبيعات

### 3. إدارة المخزون
- تتبع مستويات الوقود
- إدارة المشتريات
- تنبيهات المخزون المنخفض

### 4. إدارة الموردين
- قاعدة بيانات الموردين
- تتبع المشتريات
- إدارة الفواتير

### 5. النظام المالي
- إدارة الديون
- تتبع المدفوعات
- التقارير المالية

## 🔐 نظام الأمان
- تشفير كلمات المرور
- إدارة الجلسات
- نظام الأدوار والصلاحيات
- حماية من CSRF
- تسجيل العمليات

## 📱 واجهة المستخدم
- تصميم متجاوب يدعم جميع الأحجام
- وضع مظلم وفاتح
- دعم كامل للغة العربية
- تنقل سهل وبديهي
- رسائل تأكيد وتنبيهات

## 🗄️ إدارة البيانات
- حفظ محلي في ملفات JSON
- نظام نسخ احتياطي تلقائي
- استيراد وتصدير البيانات
- تزامن البيانات

## 🚀 التشغيل والنشر
- تطبيق Electron قابل للتثبيت
- دعم Windows بشكل أساسي
- إمكانية التشغيل كتطبيق ويب
- نظام تحديثات تلقائي

## ⚠️ المشاكل الحالية المحددة
1. تعقيد في هيكل الملفات
2. تداخل في المسؤوليات
3. عدم وجود نظام إدارة حالة موحد
4. مشاكل في التزامن بين الواجهات
5. عدم وجود اختبارات
6. كود مكرر في عدة أماكن
7. عدم وجود توثيق تقني كافي
8. مشاكل في إدارة التبعيات
9. عدم وجود نظام بناء موحد
10. صعوبة في الصيانة والتطوير

## 📝 التوصيات للتطبيق الجديد
1. هيكل مجلدات أكثر تنظيماً
2. فصل الاهتمامات بوضوح
3. استخدام إطار عمل حديث
4. نظام إدارة حالة موحد
5. اختبارات شاملة
6. توثيق تقني مفصل
7. نظام بناء ونشر محسن
8. أمان محسن
9. أداء أفضل
10. قابلية صيانة عالية
