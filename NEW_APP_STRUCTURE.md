# هيكل التطبيق الجديد - مؤسسة وقود المستقبل

## 📁 الهيكل العام

```
future-fuel-v3/
├── 📁 src/
│   ├── 📁 main/                     # Electron Main Process
│   │   ├── main.ts                  # نقطة دخول Electron
│   │   ├── preload.ts               # Preload script آمن
│   │   ├── menu.ts                  # قوائم التطبيق
│   │   ├── window-manager.ts        # إدارة النوافذ
│   │   ├── ipc-handlers.ts          # معالجات IPC
│   │   └── app-updater.ts           # نظام التحديثات
│   │
│   ├── 📁 renderer/                 # React Frontend
│   │   ├── 📁 components/           # مكونات قابلة للإعادة
│   │   │   ├── 📁 common/           # مكونات عامة
│   │   │   │   ├── Button/
│   │   │   │   ├── Modal/
│   │   │   │   ├── Table/
│   │   │   │   ├── Form/
│   │   │   │   └── Layout/
│   │   │   ├── 📁 charts/           # مكونات الرسوم البيانية
│   │   │   └── 📁 icons/            # أيقونات مخصصة
│   │   │
│   │   ├── 📁 pages/                # صفحات التطبيق
│   │   │   ├── 📁 auth/             # صفحات المصادقة
│   │   │   │   ├── Login/
│   │   │   │   └── Register/
│   │   │   ├── 📁 dashboard/        # لوحة التحكم
│   │   │   ├── 📁 customers/        # إدارة العملاء
│   │   │   ├── 📁 sales/            # إدارة المبيعات
│   │   │   ├── 📁 inventory/        # إدارة المخزون
│   │   │   ├── 📁 suppliers/        # إدارة الموردين
│   │   │   ├── 📁 financial/        # النظام المالي
│   │   │   ├── 📁 reports/          # التقارير
│   │   │   └── 📁 settings/         # الإعدادات
│   │   │
│   │   ├── 📁 hooks/                # Custom React Hooks
│   │   │   ├── useAuth.ts
│   │   │   ├── useLocalStorage.ts
│   │   │   ├── useApi.ts
│   │   │   └── useTheme.ts
│   │   │
│   │   ├── 📁 store/                # Redux Store
│   │   │   ├── index.ts             # Store configuration
│   │   │   ├── 📁 slices/           # Redux slices
│   │   │   │   ├── authSlice.ts
│   │   │   │   ├── customersSlice.ts
│   │   │   │   ├── salesSlice.ts
│   │   │   │   ├── inventorySlice.ts
│   │   │   │   └── settingsSlice.ts
│   │   │   └── 📁 middleware/       # Custom middleware
│   │   │
│   │   ├── 📁 services/             # API Services
│   │   │   ├── api.ts               # API client configuration
│   │   │   ├── authService.ts
│   │   │   ├── customersService.ts
│   │   │   ├── salesService.ts
│   │   │   ├── inventoryService.ts
│   │   │   └── reportsService.ts
│   │   │
│   │   ├── 📁 utils/                # مساعدات ووظائف مفيدة
│   │   │   ├── formatters.ts        # تنسيق البيانات
│   │   │   ├── validators.ts        # التحقق من البيانات
│   │   │   ├── constants.ts         # الثوابت
│   │   │   ├── helpers.ts           # وظائف مساعدة
│   │   │   └── dateUtils.ts         # وظائف التاريخ
│   │   │
│   │   ├── 📁 types/                # TypeScript Types
│   │   │   ├── auth.types.ts
│   │   │   ├── customer.types.ts
│   │   │   ├── sales.types.ts
│   │   │   ├── inventory.types.ts
│   │   │   └── common.types.ts
│   │   │
│   │   ├── 📁 assets/               # الموارد الثابتة
│   │   │   ├── 📁 images/
│   │   │   ├── 📁 icons/
│   │   │   ├── 📁 fonts/
│   │   │   └── 📁 styles/
│   │   │       ├── globals.css
│   │   │       ├── variables.css
│   │   │       └── themes.css
│   │   │
│   │   ├── App.tsx                  # مكون التطبيق الرئيسي
│   │   ├── index.tsx                # نقطة دخول React
│   │   └── index.html               # HTML template
│   │
│   ├── 📁 server/                   # Backend API
│   │   ├── 📁 controllers/          # معالجات الطلبات
│   │   │   ├── authController.ts
│   │   │   ├── customersController.ts
│   │   │   ├── salesController.ts
│   │   │   ├── inventoryController.ts
│   │   │   └── reportsController.ts
│   │   │
│   │   ├── 📁 models/               # نماذج البيانات
│   │   │   ├── User.ts
│   │   │   ├── Customer.ts
│   │   │   ├── Sale.ts
│   │   │   ├── Product.ts
│   │   │   └── Supplier.ts
│   │   │
│   │   ├── 📁 routes/               # مسارات API
│   │   │   ├── auth.routes.ts
│   │   │   ├── customers.routes.ts
│   │   │   ├── sales.routes.ts
│   │   │   ├── inventory.routes.ts
│   │   │   └── reports.routes.ts
│   │   │
│   │   ├── 📁 middleware/           # Middleware
│   │   │   ├── auth.middleware.ts
│   │   │   ├── validation.middleware.ts
│   │   │   ├── error.middleware.ts
│   │   │   └── logging.middleware.ts
│   │   │
│   │   ├── 📁 services/             # خدمات الأعمال
│   │   │   ├── authService.ts
│   │   │   ├── customerService.ts
│   │   │   ├── salesService.ts
│   │   │   └── reportService.ts
│   │   │
│   │   ├── 📁 database/             # إعدادات قاعدة البيانات
│   │   │   ├── connection.ts
│   │   │   ├── migrations/
│   │   │   └── seeds/
│   │   │
│   │   ├── app.ts                   # Express app configuration
│   │   └── server.ts                # نقطة دخول الخادم
│   │
│   └── 📁 shared/                   # كود مشترك
│       ├── 📁 types/                # أنواع مشتركة
│       ├── 📁 constants/            # ثوابت مشتركة
│       ├── 📁 utils/                # مساعدات مشتركة
│       └── 📁 validators/           # التحقق المشترك
│
├── 📁 tests/                        # الاختبارات
│   ├── 📁 unit/                     # اختبارات الوحدة
│   ├── 📁 integration/              # اختبارات التكامل
│   ├── 📁 e2e/                      # اختبارات شاملة
│   └── 📁 fixtures/                 # بيانات اختبار
│
├── 📁 docs/                         # التوثيق
│   ├── API.md                       # توثيق API
│   ├── SETUP.md                     # دليل التثبيت
│   ├── DEVELOPMENT.md               # دليل التطوير
│   └── DEPLOYMENT.md                # دليل النشر
│
├── 📁 scripts/                      # سكريبتات البناء والنشر
│   ├── build.js
│   ├── dev.js
│   ├── test.js
│   └── deploy.js
│
├── 📁 config/                       # ملفات التكوين
│   ├── webpack.config.js
│   ├── tsconfig.json
│   ├── jest.config.js
│   └── eslint.config.js
│
├── 📁 assets/                       # موارد التطبيق
│   ├── 📁 icons/
│   ├── 📁 images/
│   └── 📁 fonts/
│
├── package.json                     # تبعيات المشروع
├── tsconfig.json                    # إعدادات TypeScript
├── .eslintrc.js                     # إعدادات ESLint
├── .prettierrc                      # إعدادات Prettier
├── .gitignore                       # ملفات Git المتجاهلة
└── README.md                        # دليل المشروع
```

## 🔧 التقنيات المستخدمة

### Frontend:
- **React 18** مع TypeScript
- **Material-UI v5** مع دعم RTL
- **Redux Toolkit** لإدارة الحالة
- **React Router v6** للتنقل
- **React Hook Form** للنماذج
- **Chart.js** للرسوم البيانية
- **Date-fns** للتواريخ

### Backend:
- **Node.js** مع TypeScript
- **Express.js** للخادم
- **SQLite** للبيانات المحلية
- **Prisma** لـ ORM
- **JWT** للمصادقة
- **Bcrypt** للتشفير
- **Joi** للتحقق من البيانات

### Desktop:
- **Electron** للتطبيق المكتبي
- **Electron Builder** للبناء
- **Auto Updater** للتحديثات

### Development:
- **Webpack** للبناء
- **Jest** للاختبارات
- **ESLint + Prettier** لجودة الكود
- **Husky** لـ Git hooks

## 📋 الميزات الرئيسية

1. **معمارية نظيفة** مع فصل الاهتمامات
2. **Type Safety** مع TypeScript
3. **State Management** محسن مع Redux
4. **UI/UX** حديث مع Material-UI
5. **Testing** شامل مع Jest
6. **Performance** محسن مع Code Splitting
7. **Security** محسن مع أفضل الممارسات
8. **Maintainability** عالية مع كود منظم
