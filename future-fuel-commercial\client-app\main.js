/**
 * تطبيق العميل المحمي - مؤسسة وقود المستقبل
 * Protected Client Application - Future Fuel Corporation
 * 
 * تطبيق Electron محمي بالكامل مع نظام ترخيص متقدم
 */

const { app, BrowserWindow, Menu, dialog, ipcMain, shell } = require('electron');
const path = require('path');
const AuthenticationSystem = require('../shared/security/authentication');

// تعطيل تحذيرات الأمان في بيئة الإنتاج
process.env.NODE_ENV = 'production';

// المتغيرات العامة
let mainWindow;
let authSystem;
let isAuthenticated = false;

// التحقق من وجود نسخة واحدة فقط
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    app.on('second-instance', () => {
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });
}

/**
 * تهيئة التطبيق
 */
async function initializeApp() {
    try {
        // تهيئة نظام المصادقة
        authSystem = new AuthenticationSystem();
        await authSystem.initialize();

        // التحقق من الجلسة الحالية
        const sessionCheck = await authSystem.checkCurrentSession();
        isAuthenticated = sessionCheck.isValid;

        console.log('✅ تم تهيئة التطبيق بنجاح');
        console.log('🔐 حالة المصادقة:', isAuthenticated ? 'مصادق عليه' : 'غير مصادق');

    } catch (error) {
        console.error('❌ خطأ في تهيئة التطبيق:', error);
        
        // عرض رسالة خطأ للمستخدم
        dialog.showErrorBox(
            'خطأ في التهيئة',
            'حدث خطأ في تهيئة التطبيق. يرجى إعادة تشغيل التطبيق أو الاتصال بالدعم التقني.'
        );
        
        app.quit();
    }
}

/**
 * إنشاء النافذة الرئيسية
 */
function createMainWindow() {
    // تحديد حجم النافذة حسب حالة المصادقة
    const windowConfig = {
        title: isAuthenticated ? 'مؤسسة وقود المستقبل - نظام الإدارة' : 'مؤسسة وقود المستقبل - تسجيل الدخول',
        width: isAuthenticated ? 1400 : 900,
        height: isAuthenticated ? 900 : 700,
        minWidth: isAuthenticated ? 1000 : 700,
        minHeight: isAuthenticated ? 700 : 500,
        webPreferences: {
            preload: path.join(__dirname, 'preload.js'),
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            webSecurity: true,
            allowRunningInsecureContent: false,
            experimentalFeatures: false
        },
        icon: path.join(__dirname, 'assets/icons/app-icon.ico'),
        show: false,
        center: true,
        titleBarStyle: 'default',
        frame: true,
        resizable: true,
        maximizable: isAuthenticated,
        minimizable: true,
        closable: true,
        autoHideMenuBar: false
    };

    mainWindow = new BrowserWindow(windowConfig);

    // تحميل الصفحة المناسبة
    if (isAuthenticated) {
        mainWindow.loadFile(path.join(__dirname, 'src/main-app.html'));
    } else {
        mainWindow.loadFile(path.join(__dirname, 'src/login.html'));
    }

    // إظهار النافذة عند اكتمال التحميل
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // التركيز على النافذة
        if (process.platform === 'darwin') {
            app.dock.show();
        }
        mainWindow.focus();
    });

    // منع التنقل لمواقع خارجية
    mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        // السماح فقط بالملفات المحلية
        if (parsedUrl.protocol !== 'file:') {
            event.preventDefault();
            console.warn('🚫 محاولة تنقل غير مصرح إلى:', navigationUrl);
        }
    });

    // منع فتح نوافذ جديدة
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        console.warn('🚫 محاولة فتح نافذة جديدة غير مصرح:', url);
        return { action: 'deny' };
    });

    // إنشاء قائمة التطبيق
    createApplicationMenu();

    // معالجة إغلاق النافذة
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // معالجة محاولة إغلاق التطبيق
    mainWindow.on('close', async (event) => {
        if (isAuthenticated) {
            const choice = dialog.showMessageBoxSync(mainWindow, {
                type: 'question',
                buttons: ['نعم', 'لا'],
                defaultId: 1,
                title: 'تأكيد الإغلاق',
                message: 'هل تريد إغلاق التطبيق؟',
                detail: 'سيتم حفظ جميع البيانات تلقائياً.'
            });

            if (choice === 1) {
                event.preventDefault();
                return;
            }
        }

        // تسجيل الخروج عند الإغلاق
        if (authSystem && isAuthenticated) {
            await authSystem.logout();
        }
    });
}

/**
 * إنشاء قائمة التطبيق
 */
function createApplicationMenu() {
    const menuTemplate = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'حفظ البيانات',
                    accelerator: 'CmdOrCtrl+S',
                    enabled: isAuthenticated,
                    click: () => {
                        mainWindow.webContents.send('save-data');
                    }
                },
                {
                    label: 'طباعة',
                    accelerator: 'CmdOrCtrl+P',
                    enabled: isAuthenticated,
                    click: () => {
                        mainWindow.webContents.print();
                    }
                },
                { type: 'separator' },
                {
                    label: isAuthenticated ? 'تسجيل الخروج' : 'خروج',
                    accelerator: 'CmdOrCtrl+Q',
                    click: async () => {
                        if (isAuthenticated) {
                            await handleLogout();
                        } else {
                            app.quit();
                        }
                    }
                }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                {
                    label: 'إعادة تحميل',
                    accelerator: 'F5',
                    click: () => {
                        mainWindow.reload();
                    }
                },
                {
                    label: 'تكبير',
                    accelerator: 'CmdOrCtrl+Plus',
                    click: () => {
                        const currentZoom = mainWindow.webContents.getZoomLevel();
                        mainWindow.webContents.setZoomLevel(currentZoom + 1);
                    }
                },
                {
                    label: 'تصغير',
                    accelerator: 'CmdOrCtrl+-',
                    click: () => {
                        const currentZoom = mainWindow.webContents.getZoomLevel();
                        mainWindow.webContents.setZoomLevel(currentZoom - 1);
                    }
                },
                {
                    label: 'الحجم الطبيعي',
                    accelerator: 'CmdOrCtrl+0',
                    click: () => {
                        mainWindow.webContents.setZoomLevel(0);
                    }
                }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'دليل المستخدم',
                    click: () => {
                        // فتح دليل المستخدم
                        shell.openExternal('https://futurefuel.sa/help');
                    }
                },
                {
                    label: 'الدعم التقني',
                    click: () => {
                        shell.openExternal('mailto:<EMAIL>');
                    }
                },
                { type: 'separator' },
                {
                    label: 'حول التطبيق',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول التطبيق',
                            message: 'مؤسسة وقود المستقبل',
                            detail: `الإصدار: 3.0.0\nنظام إدارة شامل للمؤسسات\n\n© 2024 مؤسسة وقود المستقبل\nجميع الحقوق محفوظة`
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(menuTemplate);
    Menu.setApplicationMenu(menu);
}

/**
 * معالجة تسجيل الخروج
 */
async function handleLogout() {
    try {
        const result = await authSystem.logout();
        if (result.success) {
            isAuthenticated = false;
            
            // إعادة إنشاء النافذة لتسجيل الدخول
            if (mainWindow) {
                mainWindow.close();
            }
            createMainWindow();
        }
    } catch (error) {
        console.error('خطأ في تسجيل الخروج:', error);
    }
}

// معالجات IPC للتواصل مع واجهة المستخدم
ipcMain.handle('login', async (event, credentials) => {
    try {
        const result = await authSystem.login(
            credentials.username,
            credentials.password,
            credentials.licenseKey
        );

        if (result.success) {
            isAuthenticated = true;
            
            // إعادة إنشاء النافذة للتطبيق الرئيسي
            setTimeout(() => {
                if (mainWindow) {
                    mainWindow.close();
                }
                createMainWindow();
            }, 1000);
        }

        return result;
    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        return {
            success: false,
            message: 'حدث خطأ في النظام'
        };
    }
});

ipcMain.handle('check-session', async () => {
    return await authSystem.checkCurrentSession();
});

ipcMain.handle('logout', async () => {
    return await handleLogout();
});

// أحداث التطبيق
app.whenReady().then(async () => {
    await initializeApp();
    createMainWindow();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createMainWindow();
    }
});

// منع إنشاء نوافذ إضافية
app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        console.warn('🚫 محاولة فتح نافذة جديدة غير مصرح:', navigationUrl);
    });
});

console.log('🚀 تم تشغيل تطبيق العميل المحمي - مؤسسة وقود المستقبل');
