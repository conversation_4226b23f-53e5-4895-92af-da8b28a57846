// خادم اختبار مبسط
console.log('🚀 بدء تشغيل خادم الاختبار...');

const http = require('http');
const fs = require('fs');
const path = require('path');

// إنشاء مجلد البيانات إذا لم يكن موجوداً
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir);
    console.log('📁 تم إنشاء مجلد البيانات');
}

// إنشاء ملفات البيانات الأساسية
const files = {
    'activation-requests.json': [],
    'licenses.json': [],
    'settings.json': {
        serverPort: 3000,
        autoBackup: true,
        defaultDuration: 365,
        autoApprove: false
    }
};

Object.keys(files).forEach(filename => {
    const filePath = path.join(dataDir, filename);
    if (!fs.existsSync(filePath)) {
        fs.writeFileSync(filePath, JSON.stringify(files[filename], null, 2));
        console.log(`📄 تم إنشاء ملف: ${filename}`);
    }
});

// إنشاء الخادم
const server = http.createServer((req, res) => {
    // إعداد CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PATCH, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    console.log(`📥 طلب جديد: ${req.method} ${req.url}`);

    if (req.method === 'POST' && req.url === '/api/activation-request') {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                const requestData = JSON.parse(body);
                console.log('📋 بيانات الطلب:', requestData);
                
                // قراءة الطلبات الحالية
                const requestsFile = path.join(dataDir, 'activation-requests.json');
                const requests = JSON.parse(fs.readFileSync(requestsFile, 'utf8'));
                
                // إنشاء طلب جديد
                const newRequest = {
                    id: 'req_' + Date.now(),
                    ...requestData,
                    status: 'pending',
                    date: new Date().toISOString()
                };
                
                requests.push(newRequest);
                fs.writeFileSync(requestsFile, JSON.stringify(requests, null, 2));
                
                console.log(`✅ تم حفظ طلب التفعيل: ${newRequest.id}`);
                
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: true,
                    message: 'تم إرسال طلب التفعيل بنجاح',
                    requestId: newRequest.id
                }));
                
            } catch (error) {
                console.error('❌ خطأ في معالجة الطلب:', error);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    error: 'حدث خطأ في الخادم'
                }));
            }
        });
    } else if (req.method === 'GET' && req.url === '/api/activation-requests') {
        try {
            const requestsFile = path.join(dataDir, 'activation-requests.json');
            const requests = JSON.parse(fs.readFileSync(requestsFile, 'utf8'));
            
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                data: requests
            }));
        } catch (error) {
            console.error('❌ خطأ في قراءة الطلبات:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: false,
                error: 'حدث خطأ في قراءة البيانات'
            }));
        }
    } else {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            success: false,
            error: 'المسار غير موجود'
        }));
    }
});

const PORT = 3000;
server.listen(PORT, () => {
    console.log(`🌐 خادم الاختبار يعمل على المنفذ ${PORT}`);
    console.log(`📊 لوحة التحكم: http://localhost:${PORT}`);
    console.log(`🔗 API: http://localhost:${PORT}/api`);
    console.log(`✅ الخادم جاهز لاستقبال الطلبات`);
});

server.on('error', (error) => {
    console.error('❌ خطأ في الخادم:', error);
});
