/**
 * خادم API - ربط تطبيق العميل بلوحة المطور
 * API Server - Connect Client App with Developer Dashboard
 */

const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3001;

// إعداد CORS والمتوسطات
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// تخزين مؤقت للطلبات
let activationRequests = [];
let connectedClients = [];
let licenses = [];

// معرف فريد للطلبات
let requestIdCounter = 1;

// الصفحة الرئيسية
app.get('/', (req, res) => {
    res.json({
        message: 'خادم API - مؤسسة وقود المستقبل',
        status: 'يعمل بنجاح',
        version: '3.0.0',
        endpoints: {
            activation: '/api/activation',
            licenses: '/api/licenses',
            clients: '/api/clients',
            status: '/api/status'
        }
    });
});

// طلب تفعيل جديد من العميل
app.post('/api/activation/request', (req, res) => {
    const { clientInfo, deviceInfo, requestData } = req.body;
    
    const newRequest = {
        id: requestIdCounter++,
        clientInfo: clientInfo || {},
        deviceInfo: deviceInfo || {},
        requestData: requestData || {},
        timestamp: new Date().toISOString(),
        status: 'pending',
        createdAt: new Date().toLocaleString('ar-SA')
    };
    
    activationRequests.push(newRequest);
    
    console.log(`📨 طلب تفعيل جديد - ID: ${newRequest.id}`);
    
    res.json({
        success: true,
        message: 'تم استلام طلب التفعيل بنجاح',
        requestId: newRequest.id,
        status: 'pending'
    });
});

// الحصول على جميع طلبات التفعيل (للمطور)
app.get('/api/activation/requests', (req, res) => {
    res.json({
        success: true,
        requests: activationRequests,
        total: activationRequests.length
    });
});

// الموافقة على طلب تفعيل
app.post('/api/activation/approve/:id', (req, res) => {
    const requestId = parseInt(req.params.id);
    const { licenseData } = req.body;
    
    const request = activationRequests.find(r => r.id === requestId);
    if (!request) {
        return res.status(404).json({
            success: false,
            message: 'طلب التفعيل غير موجود'
        });
    }
    
    // إنشاء ترخيص جديد
    const newLicense = {
        id: Date.now(),
        requestId: requestId,
        clientInfo: request.clientInfo,
        licenseKey: `FL-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        status: 'active',
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // سنة واحدة
        ...licenseData
    };
    
    licenses.push(newLicense);
    request.status = 'approved';
    request.licenseId = newLicense.id;
    
    console.log(`✅ تم الموافقة على طلب التفعيل - ID: ${requestId}`);
    
    res.json({
        success: true,
        message: 'تم الموافقة على طلب التفعيل',
        license: newLicense
    });
});

// رفض طلب تفعيل
app.post('/api/activation/reject/:id', (req, res) => {
    const requestId = parseInt(req.params.id);
    const { reason } = req.body;
    
    const request = activationRequests.find(r => r.id === requestId);
    if (!request) {
        return res.status(404).json({
            success: false,
            message: 'طلب التفعيل غير موجود'
        });
    }
    
    request.status = 'rejected';
    request.rejectionReason = reason || 'لم يتم تحديد السبب';
    
    console.log(`❌ تم رفض طلب التفعيل - ID: ${requestId}`);
    
    res.json({
        success: true,
        message: 'تم رفض طلب التفعيل'
    });
});

// الحصول على حالة طلب التفعيل
app.get('/api/activation/status/:id', (req, res) => {
    const requestId = parseInt(req.params.id);
    const request = activationRequests.find(r => r.id === requestId);
    
    if (!request) {
        return res.status(404).json({
            success: false,
            message: 'طلب التفعيل غير موجود'
        });
    }
    
    res.json({
        success: true,
        request: request
    });
});

// الحصول على جميع التراخيص
app.get('/api/licenses', (req, res) => {
    res.json({
        success: true,
        licenses: licenses,
        total: licenses.length
    });
});

// التحقق من صحة الترخيص
app.post('/api/licenses/validate', (req, res) => {
    const { licenseKey, deviceInfo } = req.body;
    
    const license = licenses.find(l => l.licenseKey === licenseKey);
    
    if (!license) {
        return res.status(404).json({
            success: false,
            message: 'الترخيص غير موجود'
        });
    }
    
    if (license.status !== 'active') {
        return res.status(403).json({
            success: false,
            message: 'الترخيص غير نشط'
        });
    }
    
    if (new Date() > new Date(license.expiresAt)) {
        return res.status(403).json({
            success: false,
            message: 'انتهت صلاحية الترخيص'
        });
    }
    
    res.json({
        success: true,
        message: 'الترخيص صالح',
        license: license
    });
});

// حالة الخادم
app.get('/api/status', (req, res) => {
    res.json({
        success: true,
        server: 'يعمل بنجاح',
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        stats: {
            activationRequests: activationRequests.length,
            activeLicenses: licenses.filter(l => l.status === 'active').length,
            connectedClients: connectedClients.length
        }
    });
});

// تشغيل الخادم
app.listen(PORT, () => {
    console.log(`🚀 خادم API يعمل على المنفذ ${PORT}`);
    console.log(`📡 الرابط: http://localhost:${PORT}`);
    console.log(`📋 حالة الخادم: http://localhost:${PORT}/api/status`);
});

// معالجة الأخطاء
process.on('uncaughtException', (error) => {
    console.error('❌ خطأ غير متوقع:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ رفض غير معالج:', reason);
});
