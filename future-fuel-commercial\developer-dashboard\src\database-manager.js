/**
 * مدير قاعدة البيانات - لوحة تحكم المطور
 * Database Manager - Developer Dashboard
 * 
 * يدير الاتصال بقاعدة البيانات والعمليات الأساسية
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs').promises;

class DatabaseManager {
    constructor() {
        this.db = null;
        this.dbPath = path.join(__dirname, '../data/developer.db');
        this.isInitialized = false;
    }

    /**
     * تهيئة قاعدة البيانات
     */
    async initialize() {
        try {
            // إنشاء مجلد البيانات إذا لم يكن موجوداً
            await this.ensureDataDirectory();
            
            // الاتصال بقاعدة البيانات
            await this.connect();
            
            // تفعيل المفاتيح الخارجية
            await this.run('PRAGMA foreign_keys = ON');
            
            // تحسين الأداء
            await this.optimizeDatabase();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
            throw error;
        }
    }

    /**
     * التأكد من وجود مجلد البيانات
     */
    async ensureDataDirectory() {
        const dataDir = path.dirname(this.dbPath);
        
        try {
            await fs.access(dataDir);
        } catch (error) {
            // إنشاء المجلد إذا لم يكن موجوداً
            await fs.mkdir(dataDir, { recursive: true });
            console.log('📁 تم إنشاء مجلد البيانات:', dataDir);
        }
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    async connect() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (error) => {
                if (error) {
                    console.error('خطأ في الاتصال بقاعدة البيانات:', error);
                    reject(error);
                } else {
                    console.log('🔗 تم الاتصال بقاعدة البيانات بنجاح');
                    resolve();
                }
            });
        });
    }

    /**
     * تحسين قاعدة البيانات
     */
    async optimizeDatabase() {
        const optimizations = [
            'PRAGMA journal_mode = WAL',
            'PRAGMA synchronous = NORMAL',
            'PRAGMA cache_size = 10000',
            'PRAGMA temp_store = MEMORY',
            'PRAGMA mmap_size = 268435456'
        ];

        for (const pragma of optimizations) {
            await this.run(pragma);
        }

        console.log('⚡ تم تحسين إعدادات قاعدة البيانات');
    }

    /**
     * تنفيذ استعلام SQL
     */
    async run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(error) {
                if (error) {
                    console.error('خطأ في تنفيذ الاستعلام:', error);
                    console.error('SQL:', sql);
                    console.error('Parameters:', params);
                    reject(error);
                } else {
                    resolve({
                        lastID: this.lastID,
                        changes: this.changes
                    });
                }
            });
        });
    }

    /**
     * الحصول على صف واحد
     */
    async get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (error, row) => {
                if (error) {
                    console.error('خطأ في الحصول على البيانات:', error);
                    console.error('SQL:', sql);
                    console.error('Parameters:', params);
                    reject(error);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * الحصول على عدة صفوف
     */
    async all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (error, rows) => {
                if (error) {
                    console.error('خطأ في الحصول على البيانات:', error);
                    console.error('SQL:', sql);
                    console.error('Parameters:', params);
                    reject(error);
                } else {
                    resolve(rows || []);
                }
            });
        });
    }

    /**
     * تنفيذ معاملة
     */
    async transaction(operations) {
        return new Promise(async (resolve, reject) => {
            try {
                await this.run('BEGIN TRANSACTION');
                
                const results = [];
                for (const operation of operations) {
                    const result = await operation();
                    results.push(result);
                }
                
                await this.run('COMMIT');
                resolve(results);
                
            } catch (error) {
                await this.run('ROLLBACK');
                console.error('خطأ في المعاملة، تم التراجع:', error);
                reject(error);
            }
        });
    }

    /**
     * إنشاء نسخة احتياطية
     */
    async createBackup(backupPath) {
        try {
            const backupDir = path.dirname(backupPath);
            await fs.mkdir(backupDir, { recursive: true });
            
            return new Promise((resolve, reject) => {
                const backup = this.db.backup(backupPath);
                
                backup.step(-1, (error) => {
                    if (error) {
                        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
                        reject(error);
                    } else {
                        backup.finish((error) => {
                            if (error) {
                                console.error('خطأ في إنهاء النسخة الاحتياطية:', error);
                                reject(error);
                            } else {
                                console.log('✅ تم إنشاء النسخة الاحتياطية:', backupPath);
                                resolve(backupPath);
                            }
                        });
                    }
                });
            });
            
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            throw error;
        }
    }

    /**
     * استعادة من نسخة احتياطية
     */
    async restoreFromBackup(backupPath) {
        try {
            // التحقق من وجود ملف النسخة الاحتياطية
            await fs.access(backupPath);
            
            // إغلاق الاتصال الحالي
            await this.close();
            
            // نسخ ملف النسخة الاحتياطية
            await fs.copyFile(backupPath, this.dbPath);
            
            // إعادة الاتصال
            await this.connect();
            await this.optimizeDatabase();
            
            console.log('✅ تم استعادة قاعدة البيانات من النسخة الاحتياطية');
            
        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            throw error;
        }
    }

    /**
     * تحليل قاعدة البيانات
     */
    async analyze() {
        try {
            await this.run('ANALYZE');
            console.log('📊 تم تحليل قاعدة البيانات');
        } catch (error) {
            console.error('خطأ في تحليل قاعدة البيانات:', error);
        }
    }

    /**
     * تنظيف قاعدة البيانات
     */
    async vacuum() {
        try {
            await this.run('VACUUM');
            console.log('🧹 تم تنظيف قاعدة البيانات');
        } catch (error) {
            console.error('خطأ في تنظيف قاعدة البيانات:', error);
        }
    }

    /**
     * الحصول على إحصائيات قاعدة البيانات
     */
    async getStatistics() {
        try {
            const tables = await this.all(`
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            `);

            const statistics = {
                totalTables: tables.length,
                tables: {}
            };

            for (const table of tables) {
                const count = await this.get(`SELECT COUNT(*) as count FROM ${table.name}`);
                const size = await this.get(`
                    SELECT SUM(pgsize) as size 
                    FROM dbstat 
                    WHERE name = ?
                `, [table.name]);

                statistics.tables[table.name] = {
                    rowCount: count.count,
                    sizeBytes: size ? size.size : 0
                };
            }

            // حجم قاعدة البيانات الإجمالي
            const dbStats = await fs.stat(this.dbPath);
            statistics.totalSizeBytes = dbStats.size;

            return statistics;

        } catch (error) {
            console.error('خطأ في الحصول على إحصائيات قاعدة البيانات:', error);
            return null;
        }
    }

    /**
     * تصدير البيانات إلى JSON
     */
    async exportToJSON(outputPath) {
        try {
            const tables = await this.all(`
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            `);

            const exportData = {
                exportDate: new Date().toISOString(),
                version: '3.0.0',
                tables: {}
            };

            for (const table of tables) {
                const data = await this.all(`SELECT * FROM ${table.name}`);
                exportData.tables[table.name] = data;
            }

            await fs.writeFile(outputPath, JSON.stringify(exportData, null, 2), 'utf8');
            console.log('📤 تم تصدير البيانات إلى:', outputPath);

            return outputPath;

        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            throw error;
        }
    }

    /**
     * استيراد البيانات من JSON
     */
    async importFromJSON(inputPath) {
        try {
            const jsonData = await fs.readFile(inputPath, 'utf8');
            const importData = JSON.parse(jsonData);

            await this.transaction(async () => {
                for (const [tableName, tableData] of Object.entries(importData.tables)) {
                    // حذف البيانات الموجودة
                    await this.run(`DELETE FROM ${tableName}`);

                    // إدراج البيانات الجديدة
                    if (tableData.length > 0) {
                        const columns = Object.keys(tableData[0]);
                        const placeholders = columns.map(() => '?').join(', ');
                        const sql = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`;

                        for (const row of tableData) {
                            const values = columns.map(col => row[col]);
                            await this.run(sql, values);
                        }
                    }
                }
            });

            console.log('📥 تم استيراد البيانات من:', inputPath);

        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            throw error;
        }
    }

    /**
     * تنظيف البيانات القديمة
     */
    async cleanupOldData(daysToKeep = 90) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
            const cutoffISO = cutoffDate.toISOString();

            const cleanupOperations = [
                // تنظيف سجل الأحداث القديم
                this.run(
                    'DELETE FROM event_log WHERE timestamp < ?',
                    [cutoffISO]
                ),
                
                // تنظيف الجلسات المنتهية الصلاحية
                this.run(
                    'DELETE FROM active_sessions WHERE last_activity < ? AND status != ?',
                    [cutoffISO, 'active']
                ),
                
                // تنظيف طلبات التفعيل القديمة المرفوضة
                this.run(
                    'DELETE FROM activation_requests WHERE request_timestamp < ? AND status = ?',
                    [cutoffISO, 'rejected']
                )
            ];

            const results = await Promise.all(cleanupOperations);
            const totalDeleted = results.reduce((sum, result) => sum + result.changes, 0);

            console.log(`🧹 تم تنظيف ${totalDeleted} سجل قديم من قاعدة البيانات`);

            return totalDeleted;

        } catch (error) {
            console.error('خطأ في تنظيف البيانات القديمة:', error);
            throw error;
        }
    }

    /**
     * إغلاق الاتصال
     */
    async close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((error) => {
                    if (error) {
                        console.error('خطأ في إغلاق قاعدة البيانات:', error);
                        reject(error);
                    } else {
                        console.log('🔒 تم إغلاق الاتصال بقاعدة البيانات');
                        this.db = null;
                        this.isInitialized = false;
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        });
    }

    /**
     * التحقق من حالة الاتصال
     */
    isConnected() {
        return this.db !== null && this.isInitialized;
    }

    /**
     * الحصول على معلومات قاعدة البيانات
     */
    async getDatabaseInfo() {
        try {
            const version = await this.get('SELECT sqlite_version() as version');
            const pageCount = await this.get('PRAGMA page_count');
            const pageSize = await this.get('PRAGMA page_size');
            const freePages = await this.get('PRAGMA freelist_count');

            return {
                sqliteVersion: version.version,
                pageCount: pageCount.page_count,
                pageSize: pageSize.page_size,
                freePages: freePages.freelist_count,
                totalSize: pageCount.page_count * pageSize.page_size,
                freeSize: freePages.freelist_count * pageSize.page_size,
                path: this.dbPath
            };

        } catch (error) {
            console.error('خطأ في الحصول على معلومات قاعدة البيانات:', error);
            return null;
        }
    }
}

module.exports = DatabaseManager;
