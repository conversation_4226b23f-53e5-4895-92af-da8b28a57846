/**
 * نظام إدارة التراخيص - لوحة التحكم
 * License Management Dashboard System
 */

class LicenseDashboard {
    constructor() {
        this.currentTab = 'requests';
        this.activationRequests = [];
        this.licenses = [];
        this.settings = {
            serverPort: 3000,
            autoBackup: true,
            defaultDuration: 365,
            autoApprove: false
        };
        
        this.init();
    }

    async init() {
        console.log('🚀 تهيئة لوحة تحكم التراخيص...');
        
        // تهيئة الأحداث
        this.initEventListeners();
        
        // تحميل البيانات
        await this.loadData();
        
        // تحديث الإحصائيات
        this.updateStatistics();
        
        // بدء خادم استقبال الطلبات
        this.startRequestServer();
        
        console.log('✅ تم تهيئة لوحة التحكم بنجاح');
    }

    initEventListeners() {
        // أحداث التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // أحداث الأزرار
        document.getElementById('refresh-requests')?.addEventListener('click', () => {
            this.loadActivationRequests();
        });

        document.getElementById('export-licenses')?.addEventListener('click', () => {
            this.exportLicenses();
        });

        document.getElementById('license-search')?.addEventListener('input', (e) => {
            this.searchLicenses(e.target.value);
        });

        // أحداث النموذج
        document.getElementById('create-license-form')?.addEventListener('submit', (e) => {
            this.handleCreateLicense(e);
        });

        document.getElementById('license-duration')?.addEventListener('change', (e) => {
            this.toggleCustomDuration(e.target.value);
        });

        // أحداث الإعدادات
        document.getElementById('save-settings')?.addEventListener('click', () => {
            this.saveSettings();
        });

        document.getElementById('reset-settings')?.addEventListener('click', () => {
            this.resetSettings();
        });

        // أحداث النوافذ المنبثقة
        this.initModalEvents();

        // أحداث تسجيل الخروج
        document.getElementById('logout-btn')?.addEventListener('click', () => {
            this.logout();
        });
    }

    initModalEvents() {
        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.close-btn, .btn-secondary').forEach(btn => {
            btn.addEventListener('click', () => {
                this.closeModals();
            });
        });

        // إغلاق النوافذ بالنقر خارجها
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModals();
                }
            });
        });

        // أحداث أزرار الموافقة والرفض
        document.getElementById('approve-request')?.addEventListener('click', () => {
            this.approveRequest();
        });

        document.getElementById('reject-request')?.addEventListener('click', () => {
            this.rejectRequest();
        });

        // أحداث إدارة التراخيص
        document.getElementById('extend-license')?.addEventListener('click', () => {
            this.extendLicense();
        });

        document.getElementById('revoke-license')?.addEventListener('click', () => {
            this.revokeLicense();
        });
    }

    switchTab(tabName) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إظهار التبويب المحدد
        document.getElementById(`${tabName}-tab`)?.classList.add('active');
        document.querySelector(`[data-tab="${tabName}"]`)?.classList.add('active');

        this.currentTab = tabName;

        // تحميل البيانات حسب التبويب
        switch (tabName) {
            case 'requests':
                this.loadActivationRequests();
                break;
            case 'licenses':
                this.loadLicenses();
                break;
            case 'create':
                this.resetCreateForm();
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }

    async loadData() {
        this.showLoading(true);
        
        try {
            // تحميل طلبات التفعيل
            await this.loadActivationRequests();
            
            // تحميل التراخيص
            await this.loadLicenses();
            
            // تحميل الإعدادات
            await this.loadSettings();
            
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
            this.showNotification('حدث خطأ في تحميل البيانات', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async loadActivationRequests() {
        try {
            // محاكاة تحميل طلبات التفعيل من الخادم
            const response = await this.fetchFromServer('/api/activation-requests');
            this.activationRequests = response.data || [];
            
            this.renderActivationRequests();
            
        } catch (error) {
            console.error('❌ خطأ في تحميل طلبات التفعيل:', error);
            
            // بيانات تجريبية في حالة عدم وجود خادم
            this.activationRequests = this.generateSampleRequests();
            this.renderActivationRequests();
        }
    }

    async loadLicenses() {
        try {
            // محاكاة تحميل التراخيص من الخادم
            const response = await this.fetchFromServer('/api/licenses');
            this.licenses = response.data || [];
            
            this.renderLicenses();
            
        } catch (error) {
            console.error('❌ خطأ في تحميل التراخيص:', error);
            
            // بيانات تجريبية في حالة عدم وجود خادم
            this.licenses = this.generateSampleLicenses();
            this.renderLicenses();
        }
    }

    async loadSettings() {
        try {
            // محاكاة تحميل الإعدادات من الخادم
            const response = await this.fetchFromServer('/api/settings');
            this.settings = { ...this.settings, ...response.data };
            
            this.renderSettings();
            
        } catch (error) {
            console.error('❌ خطأ في تحميل الإعدادات:', error);
            this.renderSettings();
        }
    }

    renderActivationRequests() {
        const tbody = document.getElementById('requests-tbody');
        if (!tbody) return;

        if (this.activationRequests.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h3>لا توجد طلبات تفعيل</h3>
                        <p>لم يتم استلام أي طلبات تفعيل جديدة</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.activationRequests.map(request => `
            <tr>
                <td>${this.formatDate(request.date)}</td>
                <td>${request.companyName}</td>
                <td>${request.contactName}</td>
                <td>${request.contactPhone}</td>
                <td>${request.contactEmail || 'غير محدد'}</td>
                <td><code>${request.deviceId}</code></td>
                <td>
                    <span class="status-badge status-${request.status}">
                        ${this.getStatusText(request.status)}
                    </span>
                </td>
                <td>
                    <button class="action-btn view" onclick="licenseDashboard.viewRequest('${request.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${request.status === 'pending' ? `
                        <button class="action-btn edit" onclick="licenseDashboard.approveRequest('${request.id}')">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="action-btn delete" onclick="licenseDashboard.rejectRequest('${request.id}')">
                            <i class="fas fa-times"></i>
                        </button>
                    ` : ''}
                </td>
            </tr>
        `).join('');
    }

    renderLicenses() {
        const tbody = document.getElementById('licenses-tbody');
        if (!tbody) return;

        if (this.licenses.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="empty-state">
                        <i class="fas fa-certificate"></i>
                        <h3>لا توجد تراخيص</h3>
                        <p>لم يتم إنشاء أي تراخيص بعد</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.licenses.map(license => `
            <tr>
                <td><code>${license.licenseNumber}</code></td>
                <td>${license.companyName}</td>
                <td>${this.formatDate(license.createdDate)}</td>
                <td>${this.formatDate(license.expiryDate)}</td>
                <td>
                    <span class="status-badge status-${license.status}">
                        ${this.getStatusText(license.status)}
                    </span>
                </td>
                <td><code>${license.deviceId}</code></td>
                <td>
                    <button class="action-btn view" onclick="licenseDashboard.viewLicense('${license.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" onclick="licenseDashboard.extendLicense('${license.id}')">
                        <i class="fas fa-calendar-plus"></i>
                    </button>
                    <button class="action-btn delete" onclick="licenseDashboard.revokeLicense('${license.id}')">
                        <i class="fas fa-ban"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    renderSettings() {
        // تحديث قيم الإعدادات في النموذج
        document.getElementById('server-port').value = this.settings.serverPort;
        document.getElementById('auto-backup').checked = this.settings.autoBackup;
        document.getElementById('default-duration').value = this.settings.defaultDuration;
        document.getElementById('auto-approve').checked = this.settings.autoApprove;
    }

    updateStatistics() {
        const pendingRequests = this.activationRequests.filter(r => r.status === 'pending').length;
        const activeLicenses = this.licenses.filter(l => l.status === 'active').length;
        const expiredLicenses = this.licenses.filter(l => l.status === 'expired').length;
        const totalClients = this.licenses.length;

        document.getElementById('pending-requests').textContent = pendingRequests;
        document.getElementById('active-licenses').textContent = activeLicenses;
        document.getElementById('expired-licenses').textContent = expiredLicenses;
        document.getElementById('total-clients').textContent = totalClients;
    }

    // المساعدات والأدوات
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    getStatusText(status) {
        const statusMap = {
            'pending': 'معلق',
            'active': 'نشط',
            'expired': 'منتهي',
            'rejected': 'مرفوض',
            'revoked': 'ملغي'
        };
        return statusMap[status] || status;
    }

    generateUniqueId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    showLoading(show) {
        let overlay = document.querySelector('.loading-overlay');
        
        if (show) {
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.className = 'loading-overlay';
                overlay.innerHTML = `
                    <div class="loading-content">
                        <div class="loading-spinner"></div>
                        <p>جاري التحميل...</p>
                    </div>
                `;
                document.body.appendChild(overlay);
            }
            overlay.style.display = 'flex';
        } else {
            if (overlay) {
                overlay.style.display = 'none';
            }
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            ${message}
        `;
        
        document.body.appendChild(notification);
        
        // إظهار الإشعار
        setTimeout(() => notification.classList.add('show'), 100);
        
        // إخفاء الإشعار بعد 5 ثوان
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }

    closeModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.style.display = 'none';
        });
    }

    async fetchFromServer(endpoint) {
        // محاكاة طلب HTTP
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // في التطبيق الحقيقي، سيتم استخدام fetch أو axios
                reject(new Error('Server not available'));
            }, 1000);
        });
    }
}

    // بيانات تجريبية
    generateSampleRequests() {
        return [
            {
                id: 'req_001',
                date: new Date().toISOString(),
                companyName: 'محطة وقود الرياض',
                contactName: 'أحمد محمد',
                contactPhone: '0501234567',
                contactEmail: '<EMAIL>',
                deviceId: 'RYD-001-2024',
                status: 'pending'
            },
            {
                id: 'req_002',
                date: new Date(Date.now() - 86400000).toISOString(),
                companyName: 'محطة الشرق للوقود',
                contactName: 'فاطمة علي',
                contactPhone: '**********',
                contactEmail: '<EMAIL>',
                deviceId: 'EST-002-2024',
                status: 'pending'
            }
        ];
    }

    generateSampleLicenses() {
        return [
            {
                id: 'lic_001',
                licenseNumber: 'LIC-2024-001',
                companyName: 'محطة النور للوقود',
                createdDate: new Date(Date.now() - **********).toISOString(),
                expiryDate: new Date(Date.now() + 29030400000).toISOString(),
                status: 'active',
                deviceId: 'NUR-001-2024',
                type: 'standard'
            },
            {
                id: 'lic_002',
                licenseNumber: 'LIC-2024-002',
                companyName: 'محطة الأمل للوقود',
                createdDate: new Date(Date.now() - 31536000000).toISOString(),
                expiryDate: new Date(Date.now() - 86400000).toISOString(),
                status: 'expired',
                deviceId: 'AML-002-2024',
                type: 'premium'
            }
        ];
    }

    // وظائف إدارة الطلبات
    async viewRequest(requestId) {
        const request = this.activationRequests.find(r => r.id === requestId);
        if (!request) return;

        const modal = document.getElementById('request-details-modal');
        const content = document.getElementById('request-details-content');

        content.innerHTML = `
            <div class="details-grid">
                <div class="detail-item">
                    <strong>اسم المؤسسة:</strong>
                    <span>${request.companyName}</span>
                </div>
                <div class="detail-item">
                    <strong>اسم المسؤول:</strong>
                    <span>${request.contactName}</span>
                </div>
                <div class="detail-item">
                    <strong>رقم الهاتف:</strong>
                    <span>${request.contactPhone}</span>
                </div>
                <div class="detail-item">
                    <strong>البريد الإلكتروني:</strong>
                    <span>${request.contactEmail || 'غير محدد'}</span>
                </div>
                <div class="detail-item">
                    <strong>معرف الجهاز:</strong>
                    <span><code>${request.deviceId}</code></span>
                </div>
                <div class="detail-item">
                    <strong>تاريخ الطلب:</strong>
                    <span>${this.formatDate(request.date)}</span>
                </div>
                <div class="detail-item">
                    <strong>الحالة:</strong>
                    <span class="status-badge status-${request.status}">
                        ${this.getStatusText(request.status)}
                    </span>
                </div>
            </div>
        `;

        modal.style.display = 'block';
        this.currentRequestId = requestId;
    }

    async approveRequest(requestId = null) {
        const id = requestId || this.currentRequestId;
        const request = this.activationRequests.find(r => r.id === id);

        if (!request) {
            this.showNotification('لم يتم العثور على الطلب', 'error');
            return;
        }

        try {
            // إنشاء ترخيص جديد
            const license = {
                id: this.generateUniqueId(),
                licenseNumber: `LIC-${new Date().getFullYear()}-${String(this.licenses.length + 1).padStart(3, '0')}`,
                companyName: request.companyName,
                contactName: request.contactName,
                contactPhone: request.contactPhone,
                contactEmail: request.contactEmail,
                deviceId: request.deviceId,
                createdDate: new Date().toISOString(),
                expiryDate: new Date(Date.now() + (this.settings.defaultDuration * 24 * 60 * 60 * 1000)).toISOString(),
                status: 'active',
                type: 'standard',
                notes: `تم إنشاؤه من طلب التفعيل ${request.id}`
            };

            // إضافة الترخيص
            this.licenses.push(license);

            // تحديث حالة الطلب
            request.status = 'approved';

            // حفظ البيانات
            await this.saveLicense(license);
            await this.updateRequestStatus(request.id, 'approved');

            this.showNotification(`تم إنشاء الترخيص ${license.licenseNumber} بنجاح`, 'success');

            // تحديث العرض
            this.renderActivationRequests();
            this.renderLicenses();
            this.updateStatistics();
            this.closeModals();

        } catch (error) {
            console.error('❌ خطأ في الموافقة على الطلب:', error);
            this.showNotification('حدث خطأ أثناء الموافقة على الطلب', 'error');
        }
    }

    async rejectRequest(requestId = null) {
        const id = requestId || this.currentRequestId;
        const request = this.activationRequests.find(r => r.id === id);

        if (!request) {
            this.showNotification('لم يتم العثور على الطلب', 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من رفض طلب التفعيل لـ ${request.companyName}؟`)) {
            try {
                // تحديث حالة الطلب
                request.status = 'rejected';

                // حفظ التحديث
                await this.updateRequestStatus(request.id, 'rejected');

                this.showNotification('تم رفض طلب التفعيل', 'warning');

                // تحديث العرض
                this.renderActivationRequests();
                this.updateStatistics();
                this.closeModals();

            } catch (error) {
                console.error('❌ خطأ في رفض الطلب:', error);
                this.showNotification('حدث خطأ أثناء رفض الطلب', 'error');
            }
        }
    }

    // وظائف إدارة التراخيص
    async viewLicense(licenseId) {
        const license = this.licenses.find(l => l.id === licenseId);
        if (!license) return;

        const modal = document.getElementById('license-details-modal');
        const content = document.getElementById('license-details-content');

        content.innerHTML = `
            <div class="details-grid">
                <div class="detail-item">
                    <strong>رقم الترخيص:</strong>
                    <span><code>${license.licenseNumber}</code></span>
                </div>
                <div class="detail-item">
                    <strong>اسم المؤسسة:</strong>
                    <span>${license.companyName}</span>
                </div>
                <div class="detail-item">
                    <strong>اسم المسؤول:</strong>
                    <span>${license.contactName || 'غير محدد'}</span>
                </div>
                <div class="detail-item">
                    <strong>رقم الهاتف:</strong>
                    <span>${license.contactPhone || 'غير محدد'}</span>
                </div>
                <div class="detail-item">
                    <strong>معرف الجهاز:</strong>
                    <span><code>${license.deviceId}</code></span>
                </div>
                <div class="detail-item">
                    <strong>تاريخ الإنشاء:</strong>
                    <span>${this.formatDate(license.createdDate)}</span>
                </div>
                <div class="detail-item">
                    <strong>تاريخ الانتهاء:</strong>
                    <span>${this.formatDate(license.expiryDate)}</span>
                </div>
                <div class="detail-item">
                    <strong>نوع الترخيص:</strong>
                    <span>${license.type}</span>
                </div>
                <div class="detail-item">
                    <strong>الحالة:</strong>
                    <span class="status-badge status-${license.status}">
                        ${this.getStatusText(license.status)}
                    </span>
                </div>
                ${license.notes ? `
                    <div class="detail-item full-width">
                        <strong>ملاحظات:</strong>
                        <span>${license.notes}</span>
                    </div>
                ` : ''}
            </div>
        `;

        modal.style.display = 'block';
        this.currentLicenseId = licenseId;
    }

    async extendLicense(licenseId = null) {
        const id = licenseId || this.currentLicenseId;
        const license = this.licenses.find(l => l.id === id);

        if (!license) {
            this.showNotification('لم يتم العثور على الترخيص', 'error');
            return;
        }

        const extensionDays = prompt('كم يوماً تريد تمديد الترخيص؟', '365');

        if (extensionDays && !isNaN(extensionDays) && extensionDays > 0) {
            try {
                const currentExpiry = new Date(license.expiryDate);
                const newExpiry = new Date(currentExpiry.getTime() + (parseInt(extensionDays) * 24 * 60 * 60 * 1000));

                license.expiryDate = newExpiry.toISOString();
                license.status = 'active';

                await this.updateLicense(license);

                this.showNotification(`تم تمديد الترخيص ${license.licenseNumber} لـ ${extensionDays} يوم`, 'success');

                this.renderLicenses();
                this.updateStatistics();
                this.closeModals();

            } catch (error) {
                console.error('❌ خطأ في تمديد الترخيص:', error);
                this.showNotification('حدث خطأ أثناء تمديد الترخيص', 'error');
            }
        }
    }

    async revokeLicense(licenseId = null) {
        const id = licenseId || this.currentLicenseId;
        const license = this.licenses.find(l => l.id === id);

        if (!license) {
            this.showNotification('لم يتم العثور على الترخيص', 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من إلغاء الترخيص ${license.licenseNumber}؟`)) {
            try {
                license.status = 'revoked';

                await this.updateLicense(license);

                this.showNotification(`تم إلغاء الترخيص ${license.licenseNumber}`, 'warning');

                this.renderLicenses();
                this.updateStatistics();
                this.closeModals();

            } catch (error) {
                console.error('❌ خطأ في إلغاء الترخيص:', error);
                this.showNotification('حدث خطأ أثناء إلغاء الترخيص', 'error');
            }
        }
    }

    // وظائف إنشاء ترخيص جديد
    toggleCustomDuration(value) {
        const customGroup = document.getElementById('custom-duration-group');
        if (value === 'custom') {
            customGroup.style.display = 'block';
        } else {
            customGroup.style.display = 'none';
        }
    }

    async handleCreateLicense(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const data = Object.fromEntries(formData.entries());

        // التحقق من البيانات
        if (!data.companyName || !data.contactName || !data.contactPhone || !data.deviceId || !data.licenseDuration || !data.licenseType) {
            this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        // حساب مدة الترخيص
        let durationDays;
        if (data.licenseDuration === 'custom') {
            durationDays = parseInt(data.customDuration);
            if (!durationDays || durationDays < 1) {
                this.showNotification('يرجى إدخال مدة صحيحة للترخيص', 'error');
                return;
            }
        } else {
            durationDays = parseInt(data.licenseDuration);
        }

        try {
            // إنشاء الترخيص
            const license = {
                id: this.generateUniqueId(),
                licenseNumber: `LIC-${new Date().getFullYear()}-${String(this.licenses.length + 1).padStart(3, '0')}`,
                companyName: data.companyName,
                contactName: data.contactName,
                contactPhone: data.contactPhone,
                contactEmail: data.contactEmail || null,
                deviceId: data.deviceId,
                createdDate: new Date().toISOString(),
                expiryDate: new Date(Date.now() + (durationDays * 24 * 60 * 60 * 1000)).toISOString(),
                status: 'active',
                type: data.licenseType,
                notes: data.notes || null
            };

            // إضافة الترخيص
            this.licenses.push(license);

            // حفظ الترخيص
            await this.saveLicense(license);

            this.showNotification(`تم إنشاء الترخيص ${license.licenseNumber} بنجاح`, 'success');

            // تحديث العرض
            this.renderLicenses();
            this.updateStatistics();

            // إعادة تعيين النموذج
            this.resetCreateForm();

            // التبديل إلى تبويب التراخيص
            this.switchTab('licenses');

        } catch (error) {
            console.error('❌ خطأ في إنشاء الترخيص:', error);
            this.showNotification('حدث خطأ أثناء إنشاء الترخيص', 'error');
        }
    }

    resetCreateForm() {
        const form = document.getElementById('create-license-form');
        if (form) {
            form.reset();
            this.toggleCustomDuration('');
        }
    }

    // وظائف البحث والتصدير
    searchLicenses(query) {
        const tbody = document.getElementById('licenses-tbody');
        if (!tbody) return;

        const filteredLicenses = this.licenses.filter(license =>
            license.companyName.toLowerCase().includes(query.toLowerCase()) ||
            license.licenseNumber.toLowerCase().includes(query.toLowerCase()) ||
            license.contactName?.toLowerCase().includes(query.toLowerCase()) ||
            license.deviceId.toLowerCase().includes(query.toLowerCase())
        );

        // إعادة عرض النتائج المفلترة
        if (filteredLicenses.length === 0 && query.trim() !== '') {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="empty-state">
                        <i class="fas fa-search"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم يتم العثور على تراخيص تطابق البحث "${query}"</p>
                    </td>
                </tr>
            `;
        } else {
            // عرض النتائج (نفس منطق renderLicenses ولكن مع البيانات المفلترة)
            tbody.innerHTML = filteredLicenses.map(license => `
                <tr>
                    <td><code>${license.licenseNumber}</code></td>
                    <td>${license.companyName}</td>
                    <td>${this.formatDate(license.createdDate)}</td>
                    <td>${this.formatDate(license.expiryDate)}</td>
                    <td>
                        <span class="status-badge status-${license.status}">
                            ${this.getStatusText(license.status)}
                        </span>
                    </td>
                    <td><code>${license.deviceId}</code></td>
                    <td>
                        <button class="action-btn view" onclick="licenseDashboard.viewLicense('${license.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit" onclick="licenseDashboard.extendLicense('${license.id}')">
                            <i class="fas fa-calendar-plus"></i>
                        </button>
                        <button class="action-btn delete" onclick="licenseDashboard.revokeLicense('${license.id}')">
                            <i class="fas fa-ban"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }
    }

    exportLicenses() {
        try {
            const csvContent = this.generateCSV();
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `licenses_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                this.showNotification('تم تصدير التراخيص بنجاح', 'success');
            }
        } catch (error) {
            console.error('❌ خطأ في تصدير التراخيص:', error);
            this.showNotification('حدث خطأ أثناء تصدير التراخيص', 'error');
        }
    }

    generateCSV() {
        const headers = ['رقم الترخيص', 'اسم المؤسسة', 'اسم المسؤول', 'رقم الهاتف', 'معرف الجهاز', 'تاريخ الإنشاء', 'تاريخ الانتهاء', 'الحالة', 'النوع'];
        const rows = this.licenses.map(license => [
            license.licenseNumber,
            license.companyName,
            license.contactName || '',
            license.contactPhone || '',
            license.deviceId,
            this.formatDate(license.createdDate),
            this.formatDate(license.expiryDate),
            this.getStatusText(license.status),
            license.type
        ]);

        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        return '\uFEFF' + csvContent; // إضافة BOM للدعم العربي
    }

    // وظائف الإعدادات
    async saveSettings() {
        try {
            const settings = {
                serverPort: parseInt(document.getElementById('server-port').value),
                autoBackup: document.getElementById('auto-backup').checked,
                defaultDuration: parseInt(document.getElementById('default-duration').value),
                autoApprove: document.getElementById('auto-approve').checked
            };

            // التحقق من صحة البيانات
            if (settings.serverPort < 1000 || settings.serverPort > 65535) {
                this.showNotification('رقم المنفذ يجب أن يكون بين 1000 و 65535', 'error');
                return;
            }

            if (settings.defaultDuration < 1) {
                this.showNotification('المدة الافتراضية يجب أن تكون أكبر من صفر', 'error');
                return;
            }

            this.settings = { ...this.settings, ...settings };

            // حفظ الإعدادات
            await this.saveSettingsToServer(this.settings);

            this.showNotification('تم حفظ الإعدادات بنجاح', 'success');

        } catch (error) {
            console.error('❌ خطأ في حفظ الإعدادات:', error);
            this.showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
        }
    }

    resetSettings() {
        if (confirm('هل أنت متأكد من إعادة تعيين الإعدادات إلى القيم الافتراضية؟')) {
            this.settings = {
                serverPort: 3000,
                autoBackup: true,
                defaultDuration: 365,
                autoApprove: false
            };

            this.renderSettings();
            this.showNotification('تم إعادة تعيين الإعدادات', 'info');
        }
    }

    // وظائف الخادم والحفظ
    async startRequestServer() {
        try {
            console.log(`🌐 بدء خادم استقبال الطلبات على المنفذ ${this.settings.serverPort}...`);

            // في التطبيق الحقيقي، سيتم بدء خادم Express هنا
            // هذا مجرد محاكاة

            console.log('✅ تم بدء الخادم بنجاح');
        } catch (error) {
            console.error('❌ خطأ في بدء الخادم:', error);
        }
    }

    async saveLicense(license) {
        // محاكاة حفظ الترخيص في قاعدة البيانات
        console.log('💾 حفظ الترخيص:', license.licenseNumber);
        return Promise.resolve();
    }

    async updateLicense(license) {
        // محاكاة تحديث الترخيص في قاعدة البيانات
        console.log('🔄 تحديث الترخيص:', license.licenseNumber);
        return Promise.resolve();
    }

    async updateRequestStatus(requestId, status) {
        // محاكاة تحديث حالة الطلب في قاعدة البيانات
        console.log('🔄 تحديث حالة الطلب:', requestId, status);
        return Promise.resolve();
    }

    async saveSettingsToServer(settings) {
        // محاكاة حفظ الإعدادات في الخادم
        console.log('💾 حفظ الإعدادات:', settings);
        return Promise.resolve();
    }

    logout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            window.location.href = '../auth/login.html';
        }
    }
}

// تهيئة لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.licenseDashboard = new LicenseDashboard();
});
