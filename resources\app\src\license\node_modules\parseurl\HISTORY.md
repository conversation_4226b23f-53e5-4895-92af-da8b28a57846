1.3.3 / 2019-04-15
==================

  * Fix Node.js 0.8 return value inconsistencies

1.3.2 / 2017-09-09
==================

  * perf: reduce overhead for full URLs
  * perf: unroll the "fast-path" `RegExp`

1.3.1 / 2016-01-17
==================

  * perf: enable strict mode

1.3.0 / 2014-08-09
==================

  * Add `parseurl.original` for parsing `req.originalUrl` with fallback
  * Return `undefined` if `req.url` is `undefined`

1.2.0 / 2014-07-21
==================

  * Cache URLs based on original value
  * Remove no-longer-needed URL mis-parse work-around
  * Simplify the "fast-path" `RegExp`

1.1.3 / 2014-07-08
==================

  * Fix typo

1.1.2 / 2014-07-08
==================

  * Seriously fix Node.js 0.8 compatibility

1.1.1 / 2014-07-08
==================

  * Fix Node.js 0.8 compatibility

1.1.0 / 2014-07-08
==================

  * Incorporate URL href-only parse fast-path

1.0.1 / 2014-03-08
==================

  * Add missing `require`

1.0.0 / 2014-03-08
==================

  * Genesis from `connect`
