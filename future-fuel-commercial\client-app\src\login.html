<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - مؤسسة وقود المستقبل</title>
    
    <!-- الخطوط والأيقونات -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            overflow: hidden;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
            min-height: 600px;
            display: flex;
            overflow: hidden;
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            padding: 40px;
            position: relative;
        }

        .company-logo {
            font-size: 4rem;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .company-info {
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .company-info h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .company-info p {
            font-size: 1.2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .version-info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .login-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-header h2 {
            color: #333;
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .form-group .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 1.1rem;
        }

        .form-group input.with-icon {
            padding-left: 50px;
        }

        .license-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }

        .license-section.active {
            border-color: #4facfe;
            background: rgba(79, 172, 254, 0.05);
        }

        .license-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            margin-bottom: 15px;
        }

        .license-toggle h3 {
            color: #333;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .license-toggle i {
            color: #4facfe;
            transition: transform 0.3s ease;
        }

        .license-toggle.active i {
            transform: rotate(180deg);
        }

        .license-input {
            display: none;
            animation: slideDown 0.3s ease;
        }

        .license-input.show {
            display: block;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }
            to {
                opacity: 1;
                max-height: 100px;
            }
        }

        .activation-request {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            display: none;
        }

        .activation-request.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .activation-request h4 {
            color: #856404;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .activation-request p {
            color: #856404;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .btn-login {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(79, 172, 254, 0.3);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-login .spinner {
            display: none;
            margin-left: 10px;
        }

        .btn-login.loading .spinner {
            display: inline-block;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.95rem;
            display: none;
        }

        .alert.show {
            display: block;
            animation: slideInAlert 0.5s ease;
        }

        @keyframes slideInAlert {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .footer-links {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .footer-links a {
            color: #4facfe;
            text-decoration: none;
            font-size: 0.9rem;
            margin: 0 15px;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #00f2fe;
        }

        /* تجاوبية */
        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                max-width: 400px;
                margin: 20px;
            }

            .login-left {
                padding: 30px 20px;
            }

            .company-info h1 {
                font-size: 2rem;
            }

            .company-logo {
                font-size: 3rem;
            }

            .login-right {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- الجانب الأيسر - معلومات الشركة -->
        <div class="login-left">
            <div class="company-logo">
                <i class="fas fa-gas-pump"></i>
            </div>
            <div class="company-info">
                <h1>مؤسسة وقود المستقبل</h1>
                <p>نظام إدارة شامل ومتطور<br>للمؤسسات والمحطات</p>
            </div>
            <div class="version-info">
                الإصدار 3.0.0 - النسخة التجارية
            </div>
        </div>

        <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
        <div class="login-right">
            <div class="login-header">
                <h2>تسجيل الدخول</h2>
                <p>أدخل بياناتك للوصول إلى النظام</p>
            </div>

            <!-- رسائل التنبيه -->
            <div id="alert-container"></div>

            <!-- نموذج تسجيل الدخول -->
            <form id="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <div style="position: relative;">
                        <input type="text" id="username" name="username" class="with-icon" 
                               placeholder="أدخل اسم المستخدم" required autocomplete="username">
                        <i class="fas fa-user input-icon"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div style="position: relative;">
                        <input type="password" id="password" name="password" class="with-icon" 
                               placeholder="أدخل كلمة المرور" required autocomplete="current-password">
                        <i class="fas fa-lock input-icon"></i>
                    </div>
                </div>

                <!-- قسم الترخيص -->
                <div class="license-section" id="license-section">
                    <div class="license-toggle" id="license-toggle">
                        <h3>
                            <i class="fas fa-key" style="margin-left: 8px;"></i>
                            رقم الترخيص (اختياري)
                        </h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="license-input" id="license-input">
                        <input type="text" id="license-key" name="license-key" 
                               placeholder="أدخل رقم الترخيص إذا كان متوفراً">
                        <div class="activation-request" id="activation-request">
                            <h4><i class="fas fa-info-circle"></i> طلب تفعيل جديد</h4>
                            <p>إذا لم يكن لديك رقم ترخيص، سيتم إرسال طلب تفعيل تلقائياً للمطور عند تسجيل الدخول.</p>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn-login" id="login-btn">
                    <i class="fas fa-sign-in-alt" style="margin-left: 8px;"></i>
                    تسجيل الدخول
                    <i class="fas fa-spinner spinner"></i>
                </button>
            </form>

            <!-- روابط المساعدة -->
            <div class="footer-links">
                <a href="#" id="help-link">
                    <i class="fas fa-question-circle"></i> المساعدة
                </a>
                <a href="#" id="support-link">
                    <i class="fas fa-headset"></i> الدعم التقني
                </a>
                <a href="#" id="about-link">
                    <i class="fas fa-info-circle"></i> حول التطبيق
                </a>
            </div>
        </div>
    </div>

    <script src="login.js"></script>
</body>
</html>
