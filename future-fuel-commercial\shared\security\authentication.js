/**
 * نظام المصادقة المتقدم
 * Advanced Authentication System
 * 
 * يوفر مصادقة آمنة مع إدارة الجلسات وطلبات التفعيل
 */

const crypto = require('crypto');
const ProtectionSystem = require('./protection-system');

class AuthenticationSystem {
    constructor() {
        this.protectionSystem = new ProtectionSystem();
        this.maxLoginAttempts = 3;
        this.lockoutDuration = 15 * 60 * 1000; // 15 دقيقة
        this.sessionDuration = 8 * 60 * 60 * 1000; // 8 ساعات
    }

    /**
     * تهيئة نظام المصادقة
     */
    async initialize() {
        await this.protectionSystem.initialize();
        console.log('✅ تم تهيئة نظام المصادقة');
    }

    /**
     * تسجيل الدخول
     */
    async login(username, password, licenseKey = null) {
        try {
            // التحقق من محاولات تسجيل الدخول
            if (await this.isAccountLocked(username)) {
                return {
                    success: false,
                    message: 'الحساب مقفل مؤقتاً بسبب محاولات دخول خاطئة متعددة',
                    requiresActivation: false
                };
            }

            // التحقق من بيانات المستخدم
            const userValidation = await this.validateUser(username, password);
            if (!userValidation.isValid) {
                await this.recordFailedAttempt(username);
                return {
                    success: false,
                    message: 'اسم المستخدم أو كلمة المرور غير صحيحة',
                    requiresActivation: false
                };
            }

            // التحقق من الترخيص
            const licenseValidation = await this.validateLicense(licenseKey);
            if (!licenseValidation.isValid) {
                // إذا لم يكن هناك ترخيص، إرسال طلب تفعيل
                if (!licenseKey) {
                    const activationRequest = await this.sendActivationRequest(username);
                    return {
                        success: false,
                        message: 'يتطلب تفعيل الترخيص. تم إرسال طلب التفعيل للمطور.',
                        requiresActivation: true,
                        activationRequestId: activationRequest.requestId
                    };
                } else {
                    return {
                        success: false,
                        message: 'رقم الترخيص غير صحيح أو منتهي الصلاحية',
                        requiresActivation: true
                    };
                }
            }

            // إنشاء جلسة آمنة
            const session = await this.createSecureSession(username, licenseValidation.license);
            
            // تسجيل نجاح تسجيل الدخول
            this.protectionSystem.logSecurityEvent('LOGIN_SUCCESS', {
                username: username,
                licenseId: licenseValidation.license.id
            });

            return {
                success: true,
                message: 'تم تسجيل الدخول بنجاح',
                session: session,
                user: userValidation.user,
                license: licenseValidation.license
            };

        } catch (error) {
            console.error('❌ خطأ في تسجيل الدخول:', error);
            this.protectionSystem.logSecurityEvent('LOGIN_ERROR', {
                username: username,
                error: error.message
            });
            
            return {
                success: false,
                message: 'حدث خطأ في النظام. يرجى المحاولة لاحقاً',
                requiresActivation: false
            };
        }
    }

    /**
     * التحقق من صحة بيانات المستخدم
     */
    async validateUser(username, password) {
        try {
            // هنا يمكن التحقق من قاعدة بيانات المستخدمين
            // للتبسيط، سنستخدم مستخدم افتراضي
            const defaultUsers = {
                'admin': {
                    password: this.hashPassword('admin123'),
                    role: 'administrator',
                    name: 'مدير النظام'
                },
                'user': {
                    password: this.hashPassword('user123'),
                    role: 'user',
                    name: 'مستخدم'
                }
            };

            const user = defaultUsers[username];
            if (!user) {
                return { isValid: false };
            }

            const hashedPassword = this.hashPassword(password);
            if (hashedPassword !== user.password) {
                return { isValid: false };
            }

            return {
                isValid: true,
                user: {
                    username: username,
                    name: user.name,
                    role: user.role
                }
            };

        } catch (error) {
            console.error('خطأ في التحقق من المستخدم:', error);
            return { isValid: false };
        }
    }

    /**
     * التحقق من صحة الترخيص
     */
    async validateLicense(licenseKey) {
        try {
            if (!licenseKey) {
                return { isValid: false };
            }

            // هنا سيتم التحقق من الترخيص مع خادم التراخيص
            // للتبسيط، سنقبل أي ترخيص يبدأ بـ "FL-"
            if (licenseKey.startsWith('FL-') && licenseKey.length >= 10) {
                return {
                    isValid: true,
                    license: {
                        id: licenseKey,
                        type: 'commercial',
                        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // سنة واحدة
                        features: ['full_access', 'support', 'updates']
                    }
                };
            }

            return { isValid: false };

        } catch (error) {
            console.error('خطأ في التحقق من الترخيص:', error);
            return { isValid: false };
        }
    }

    /**
     * إرسال طلب تفعيل
     */
    async sendActivationRequest(username) {
        try {
            const requestId = this.generateRequestId();
            const deviceInfo = this.protectionSystem.getDeviceInfo();

            const activationRequest = {
                requestId: requestId,
                username: username,
                deviceFingerprint: this.protectionSystem.deviceFingerprint,
                deviceInfo: deviceInfo,
                timestamp: new Date().toISOString(),
                status: 'pending'
            };

            // حفظ طلب التفعيل محلياً
            await this.saveActivationRequest(activationRequest);

            // محاولة إرسال الطلب لخادم المطور
            try {
                const response = await this.sendToActivationServer(activationRequest);
                if (response.success) {
                    activationRequest.status = 'sent';
                    activationRequest.serverRequestId = response.requestId;
                    await this.updateActivationRequest(activationRequest);

                    // بدء مراقبة حالة الطلب
                    this.startActivationMonitoring(response.requestId);
                }
            } catch (serverError) {
                console.error('خطأ في إرسال طلب التفعيل للخادم:', serverError);
                // سيبقى الطلب في حالة pending للمحاولة لاحقاً
            }

            this.protectionSystem.logSecurityEvent('ACTIVATION_REQUEST_SENT', {
                requestId: requestId,
                username: username
            });

            return activationRequest;

        } catch (error) {
            console.error('خطأ في إرسال طلب التفعيل:', error);
            throw error;
        }
    }

    /**
     * إرسال طلب التفعيل لخادم المطور
     */
    async sendToActivationServer(activationRequest) {
        const serverUrl = 'http://127.0.0.1:3001/api/activation-request';

        const requestData = {
            username: activationRequest.username,
            deviceFingerprint: activationRequest.deviceFingerprint,
            deviceInfo: activationRequest.deviceInfo
        };

        const response = await fetch(serverUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'FutureFuel-Client/3.0.0'
            },
            body: JSON.stringify(requestData),
            timeout: 10000
        });

        if (!response.ok) {
            throw new Error(`خطأ في الخادم: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    /**
     * بدء مراقبة حالة طلب التفعيل
     */
    startActivationMonitoring(serverRequestId) {
        // إيقاف المراقبة السابقة إن وجدت
        if (this.activationMonitorInterval) {
            clearInterval(this.activationMonitorInterval);
        }

        // بدء مراقبة جديدة كل 30 ثانية
        this.activationMonitorInterval = setInterval(async () => {
            try {
                await this.checkActivationStatus(serverRequestId);
            } catch (error) {
                console.error('خطأ في مراقبة حالة التفعيل:', error);
            }
        }, 30000);

        // إيقاف المراقبة بعد 24 ساعة
        setTimeout(() => {
            if (this.activationMonitorInterval) {
                clearInterval(this.activationMonitorInterval);
                this.activationMonitorInterval = null;
            }
        }, 24 * 60 * 60 * 1000);
    }

    /**
     * التحقق من حالة طلب التفعيل
     */
    async checkActivationStatus(serverRequestId) {
        try {
            const serverUrl = `http://127.0.0.1:3001/api/activation-status/${serverRequestId}`;

            const response = await fetch(serverUrl, {
                method: 'GET',
                headers: {
                    'User-Agent': 'FutureFuel-Client/3.0.0'
                },
                timeout: 5000
            });

            if (!response.ok) {
                throw new Error(`خطأ في الخادم: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                if (result.status === 'approved' && result.licenseKey) {
                    // تم الموافقة على الطلب
                    await this.handleActivationApproved(serverRequestId, result.licenseKey);

                    // إيقاف المراقبة
                    if (this.activationMonitorInterval) {
                        clearInterval(this.activationMonitorInterval);
                        this.activationMonitorInterval = null;
                    }

                } else if (result.status === 'rejected') {
                    // تم رفض الطلب
                    await this.handleActivationRejected(serverRequestId, result.message);

                    // إيقاف المراقبة
                    if (this.activationMonitorInterval) {
                        clearInterval(this.activationMonitorInterval);
                        this.activationMonitorInterval = null;
                    }
                }
            }

        } catch (error) {
            console.error('خطأ في التحقق من حالة التفعيل:', error);
        }
    }

    /**
     * معالجة الموافقة على التفعيل
     */
    async handleActivationApproved(serverRequestId, licenseKey) {
        try {
            // حفظ الترخيص
            await this.saveLicense(licenseKey);

            // تحديث حالة طلب التفعيل
            const activationRequest = await this.getActivationRequestByServerId(serverRequestId);
            if (activationRequest) {
                activationRequest.status = 'approved';
                activationRequest.licenseKey = licenseKey;
                activationRequest.approvedAt = new Date().toISOString();
                await this.updateActivationRequest(activationRequest);
            }

            // إشعار المستخدم
            console.log('✅ تم الموافقة على طلب التفعيل وحفظ الترخيص');

            // إعادة تحميل التطبيق للدخول بالترخيص الجديد
            if (typeof window !== 'undefined' && window.electronAPI) {
                window.electronAPI.system.reloadApp();
            }

        } catch (error) {
            console.error('خطأ في معالجة الموافقة على التفعيل:', error);
        }
    }

    /**
     * معالجة رفض التفعيل
     */
    async handleActivationRejected(serverRequestId, reason) {
        try {
            // تحديث حالة طلب التفعيل
            const activationRequest = await this.getActivationRequestByServerId(serverRequestId);
            if (activationRequest) {
                activationRequest.status = 'rejected';
                activationRequest.rejectionReason = reason;
                activationRequest.rejectedAt = new Date().toISOString();
                await this.updateActivationRequest(activationRequest);
            }

            console.log('❌ تم رفض طلب التفعيل:', reason);

        } catch (error) {
            console.error('خطأ في معالجة رفض التفعيل:', error);
        }
    }

    /**
     * إنشاء جلسة آمنة
     */
    async createSecureSession(username, license) {
        try {
            const sessionId = this.generateSessionId();
            const expiryDate = new Date(Date.now() + this.sessionDuration);

            const sessionData = {
                sessionId: sessionId,
                username: username,
                license: license,
                deviceFingerprint: this.protectionSystem.deviceFingerprint,
                createdAt: new Date().toISOString(),
                expiryDate: expiryDate.toISOString(),
                isValid: true
            };

            await this.protectionSystem.saveSecureSession(sessionData);
            return sessionData;

        } catch (error) {
            console.error('خطأ في إنشاء الجلسة:', error);
            throw error;
        }
    }

    /**
     * التحقق من الجلسة الحالية
     */
    async checkCurrentSession() {
        try {
            const session = await this.protectionSystem.loadSecureSession();
            if (session && this.protectionSystem.isSessionValid(session)) {
                return {
                    isValid: true,
                    session: session
                };
            }
            return { isValid: false };
        } catch (error) {
            console.error('خطأ في التحقق من الجلسة:', error);
            return { isValid: false };
        }
    }

    /**
     * تسجيل الخروج
     */
    async logout() {
        try {
            this.protectionSystem.logSecurityEvent('LOGOUT', {
                sessionId: this.protectionSystem.sessionData?.sessionId
            });
            
            await this.protectionSystem.logout();
            return { success: true };
        } catch (error) {
            console.error('خطأ في تسجيل الخروج:', error);
            return { success: false };
        }
    }

    /**
     * تشفير كلمة المرور
     */
    hashPassword(password) {
        return crypto.createHash('sha256').update(password + 'future_fuel_salt').digest('hex');
    }

    /**
     * توليد معرف الطلب
     */
    generateRequestId() {
        return 'REQ-' + crypto.randomBytes(8).toString('hex').toUpperCase();
    }

    /**
     * توليد معرف الجلسة
     */
    generateSessionId() {
        return 'SES-' + crypto.randomBytes(16).toString('hex').toUpperCase();
    }

    /**
     * التحقق من قفل الحساب
     */
    async isAccountLocked(username) {
        // سيتم تطوير هذا لاحقاً
        return false;
    }

    /**
     * تسجيل محاولة فاشلة
     */
    async recordFailedAttempt(username) {
        this.protectionSystem.logSecurityEvent('LOGIN_FAILED', { username });
    }

    /**
     * حفظ طلب التفعيل
     */
    async saveActivationRequest(activationRequest) {
        try {
            const encryptedData = this.protectionSystem.encryptData(JSON.stringify(activationRequest));
            localStorage.setItem(`activation_request_${activationRequest.requestId}`, encryptedData);

            // حفظ قائمة طلبات التفعيل
            const requests = await this.getActivationRequests();
            requests.push(activationRequest.requestId);
            const encryptedRequests = this.protectionSystem.encryptData(JSON.stringify(requests));
            localStorage.setItem('activation_requests', encryptedRequests);

        } catch (error) {
            console.error('خطأ في حفظ طلب التفعيل:', error);
        }
    }

    /**
     * تحديث طلب التفعيل
     */
    async updateActivationRequest(activationRequest) {
        try {
            const encryptedData = this.protectionSystem.encryptData(JSON.stringify(activationRequest));
            localStorage.setItem(`activation_request_${activationRequest.requestId}`, encryptedData);
        } catch (error) {
            console.error('خطأ في تحديث طلب التفعيل:', error);
        }
    }

    /**
     * الحصول على طلبات التفعيل
     */
    async getActivationRequests() {
        try {
            const encryptedRequests = localStorage.getItem('activation_requests');
            if (encryptedRequests) {
                const decryptedRequests = this.protectionSystem.decryptData(encryptedRequests);
                return JSON.parse(decryptedRequests);
            }
            return [];
        } catch (error) {
            console.error('خطأ في الحصول على طلبات التفعيل:', error);
            return [];
        }
    }

    /**
     * الحصول على طلب التفعيل بواسطة معرف الخادم
     */
    async getActivationRequestByServerId(serverRequestId) {
        try {
            const requests = await this.getActivationRequests();

            for (const requestId of requests) {
                const encryptedData = localStorage.getItem(`activation_request_${requestId}`);
                if (encryptedData) {
                    const decryptedData = this.protectionSystem.decryptData(encryptedData);
                    const request = JSON.parse(decryptedData);

                    if (request.serverRequestId === serverRequestId) {
                        return request;
                    }
                }
            }

            return null;
        } catch (error) {
            console.error('خطأ في البحث عن طلب التفعيل:', error);
            return null;
        }
    }

    /**
     * حفظ الترخيص
     */
    async saveLicense(licenseKey) {
        try {
            // التحقق من صحة الترخيص أولاً
            const validation = await this.validateLicenseWithServer(licenseKey);
            if (!validation.success) {
                throw new Error('ترخيص غير صالح');
            }

            // حفظ الترخيص مشفراً
            const licenseData = {
                key: licenseKey,
                savedAt: new Date().toISOString(),
                deviceFingerprint: this.protectionSystem.deviceFingerprint,
                features: validation.features || {},
                expiryDate: validation.expiryDate
            };

            const encryptedLicense = this.protectionSystem.encryptData(JSON.stringify(licenseData));
            localStorage.setItem('license_data', encryptedLicense);

            console.log('✅ تم حفظ الترخيص بنجاح');

        } catch (error) {
            console.error('خطأ في حفظ الترخيص:', error);
            throw error;
        }
    }

    /**
     * التحقق من صحة الترخيص مع الخادم
     */
    async validateLicenseWithServer(licenseKey) {
        try {
            const serverUrl = 'http://127.0.0.1:3001/api/validate-license';

            const requestData = {
                licenseKey: licenseKey,
                deviceFingerprint: this.protectionSystem.deviceFingerprint
            };

            const response = await fetch(serverUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'FutureFuel-Client/3.0.0'
                },
                body: JSON.stringify(requestData),
                timeout: 10000
            });

            if (!response.ok) {
                throw new Error(`خطأ في الخادم: ${response.status}`);
            }

            return await response.json();

        } catch (error) {
            console.error('خطأ في التحقق من الترخيص مع الخادم:', error);
            // في حالة عدم توفر الخادم، استخدم التحقق المحلي
            return this.validateLicense(licenseKey);
        }
    }

    /**
     * إرسال للخادم (دالة قديمة - تم استبدالها)
     */
    async sendToLicenseServer(request) {
        // تم استبدال هذه الدالة بـ sendToActivationServer
        console.warn('تم استدعاء دالة قديمة: sendToLicenseServer');
    }
}

module.exports = AuthenticationSystem;
