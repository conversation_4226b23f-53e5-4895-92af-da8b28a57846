# دليل البدء السريع - نظام إدارة التراخيص

## 🚀 تشغيل النظام بسرعة

### الخطوة 1: تشغيل خادم التراخيص

```bash
# انتقل إلى مجلد التراخيص
cd resources/app/src/license

# تشغيل الخادم (Windows)
start-server.bat

# أو تشغيل يدوي
npm install
npm start
```

### الخطوة 2: فتح لوحة التحكم

افتح الملف في المتصفح:
```
resources/app/src/license/license-dashboard.html
```

### الخطوة 3: اختبار النظام

1. **من واجهة تسجيل الدخول:**
   - افتح `resources/app/src/auth/login.html`
   - انقر على "طلب تفعيل"
   - املأ البيانات وأرسل الطلب

2. **من لوحة التحكم:**
   - ستجد الطلب في تبويب "طلبات التفعيل"
   - يمكنك الموافقة أو الرفض
   - عند الموافقة، سيتم إنشاء ترخيص تلقائياً

## 📋 الميزات الرئيسية

### ✅ ما تم تنفيذه:

1. **خادم التراخيص**
   - استقبال طلبات التفعيل من واجهة تسجيل الدخول
   - API شامل لإدارة التراخيص
   - حفظ البيانات في ملفات JSON
   - التحقق من صحة التراخيص

2. **لوحة التحكم**
   - إحصائيات في الوقت الفعلي
   - إدارة طلبات التفعيل (موافقة/رفض)
   - إنشاء تراخيص جديدة
   - تمديد وإلغاء التراخيص
   - البحث والتصدير
   - إعدادات النظام

3. **تكامل مع نظام تسجيل الدخول**
   - إرسال طلبات التفعيل تلقائياً
   - حفظ محلي في حالة عدم توفر الاتصال
   - إعادة المحاولة التلقائية

4. **واجهة المستخدم**
   - تصميم عربي متجاوب
   - نوافذ منبثقة للتفاصيل
   - إشعارات تفاعلية
   - شاشات تحميل

## 🔧 API Endpoints

```
POST /api/activation-request     # إرسال طلب تفعيل
GET  /api/activation-requests    # جلب طلبات التفعيل
PATCH /api/activation-requests/:id # تحديث طلب

GET  /api/licenses              # جلب التراخيص
POST /api/licenses              # إنشاء ترخيص
PATCH /api/licenses/:id         # تحديث ترخيص

POST /api/validate-license      # التحقق من الترخيص
GET  /api/settings              # جلب الإعدادات
POST /api/settings              # تحديث الإعدادات
```

## 📁 هيكل الملفات

```
resources/app/src/license/
├── license-dashboard.html      # واجهة لوحة التحكم
├── license-dashboard.css       # أنماط لوحة التحكم
├── license-dashboard.js        # منطق لوحة التحكم
├── license-server.js           # خادم التراخيص
├── package.json               # تبعيات Node.js
├── start-server.bat           # ملف تشغيل سريع
├── README.md                  # دليل شامل
├── QUICK_START.md            # دليل البدء السريع
└── data/                     # مجلد البيانات (يتم إنشاؤه تلقائياً)
    ├── activation-requests.json
    ├── licenses.json
    └── settings.json
```

## 🔄 سير العمل

1. **المستخدم يطلب التفعيل:**
   - من واجهة تسجيل الدخول
   - يملأ بيانات المؤسسة والمسؤول
   - يتم إرسال الطلب إلى خادم التراخيص

2. **المطور يراجع الطلب:**
   - يفتح لوحة التحكم
   - يراجع تفاصيل الطلب
   - يوافق أو يرفض

3. **إنشاء الترخيص:**
   - عند الموافقة، يتم إنشاء ترخيص تلقائياً
   - يحصل على رقم ترخيص فريد
   - يتم تحديد تاريخ الانتهاء

4. **استخدام الترخيص:**
   - التطبيق يتحقق من الترخيص عند بدء التشغيل
   - يتم التحقق من الصلاحية والحالة
   - يعرض تحذيرات عند اقتراب الانتهاء

## 🛠️ التخصيص

### تغيير المنفذ:
```javascript
// في license-server.js
this.port = process.env.PORT || 3001; // بدلاً من 3000
```

### إضافة أنواع تراخيص جديدة:
```html
<!-- في license-dashboard.html -->
<option value="custom">نوع مخصص</option>
```

### تخصيص مدة الترخيص الافتراضية:
```javascript
// في license-dashboard.js
defaultDuration: 180, // 6 أشهر بدلاً من سنة
```

## 🔍 استكشاف الأخطاء

### مشكلة: الخادم لا يعمل
```bash
# تحقق من Node.js
node --version

# تحقق من المنفذ
netstat -an | findstr :3000
```

### مشكلة: لا تظهر البيانات
- تحقق من console المتصفح للأخطاء
- تأكد من تشغيل الخادم
- راجع ملفات البيانات في مجلد `data`

### مشكلة: طلبات التفعيل لا تصل
- تحقق من عنوان الخادم في `login.js`
- تأكد من عدم حجب CORS
- راجع network tab في أدوات المطور

## 📞 الدعم

للحصول على المساعدة:
1. راجع الـ console للأخطاء
2. تحقق من ملفات البيانات
3. راجع README.md للتفاصيل الكاملة

---

**نظام إدارة التراخيص جاهز للاستخدام! 🎉**
