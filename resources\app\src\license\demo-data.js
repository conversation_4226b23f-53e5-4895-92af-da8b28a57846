// بيانات تجريبية لعرض لوحة التراخيص
window.demoData = {
    activationRequests: [
        {
            id: 'req_001',
            companyName: 'محطة وقود الرياض',
            contactName: 'أحمد محمد علي',
            contactPhone: '0501234567',
            contactEmail: '<EMAIL>',
            deviceId: 'RYD-001-2024',
            systemInfo: {
                os: 'Windows 11',
                version: '22H2',
                architecture: 'x64',
                memory: '8 GB',
                processor: 'Intel Core i5'
            },
            status: 'pending',
            date: new Date().toISOString()
        },
        {
            id: 'req_002',
            companyName: 'محطة النور للوقود',
            contactName: 'فاطمة علي حسن',
            contactPhone: '**********',
            contactEmail: '<EMAIL>',
            deviceId: 'NUR-002-2024',
            systemInfo: {
                os: 'Windows 10',
                version: '21H2',
                architecture: 'x64',
                memory: '16 GB',
                processor: 'AMD Ryzen 7'
            },
            status: 'approved',
            date: new Date(Date.now() - 86400000).toISOString() // أمس
        },
        {
            id: 'req_003',
            companyName: 'محطة الخليج',
            contactName: 'محمد سالم',
            contactPhone: '**********',
            contactEmail: '<EMAIL>',
            deviceId: 'GUL-003-2024',
            systemInfo: {
                os: 'Windows 11',
                version: '23H2',
                architecture: 'x64',
                memory: '32 GB',
                processor: 'Intel Core i7'
            },
            status: 'rejected',
            date: new Date(Date.now() - *********).toISOString() // قبل يومين
        }
    ],
    
    licenses: [
        {
            id: 'lic_001',
            licenseNumber: 'LIC-2024-001',
            companyName: 'محطة النور للوقود',
            contactName: 'فاطمة علي حسن',
            contactPhone: '**********',
            contactEmail: '<EMAIL>',
            deviceId: 'NUR-002-2024',
            createdDate: new Date(Date.now() - 86400000).toISOString(),
            expiryDate: new Date(Date.now() + (365 * 24 * 60 * 60 * 1000)).toISOString(),
            status: 'active',
            type: 'standard',
            notes: 'ترخيص قياسي لمحطة وقود متوسطة الحجم'
        },
        {
            id: 'lic_002',
            licenseNumber: 'LIC-2024-002',
            companyName: 'محطة الملك فهد',
            contactName: 'عبدالله أحمد',
            contactPhone: '**********',
            contactEmail: '<EMAIL>',
            deviceId: 'KFD-004-2024',
            createdDate: new Date(Date.now() - (30 * 24 * 60 * 60 * 1000)).toISOString(),
            expiryDate: new Date(Date.now() + (335 * 24 * 60 * 60 * 1000)).toISOString(),
            status: 'active',
            type: 'premium',
            notes: 'ترخيص مميز مع ميزات إضافية'
        },
        {
            id: 'lic_003',
            licenseNumber: 'LIC-2023-015',
            companyName: 'محطة الصحراء',
            contactName: 'سارة محمد',
            contactPhone: '**********',
            contactEmail: '<EMAIL>',
            deviceId: 'DST-005-2023',
            createdDate: new Date(Date.now() - (400 * 24 * 60 * 60 * 1000)).toISOString(),
            expiryDate: new Date(Date.now() - (35 * 24 * 60 * 60 * 1000)).toISOString(),
            status: 'expired',
            type: 'standard',
            notes: 'ترخيص منتهي الصلاحية - يحتاج تجديد'
        }
    ],
    
    settings: {
        serverPort: 3000,
        autoBackup: true,
        defaultDuration: 365,
        autoApprove: false,
        emailNotifications: true,
        backupInterval: 24
    }
};

// إحصائيات محسوبة
window.demoData.stats = {
    pendingRequests: window.demoData.activationRequests.filter(req => req.status === 'pending').length,
    activeLicenses: window.demoData.licenses.filter(lic => lic.status === 'active').length,
    expiredLicenses: window.demoData.licenses.filter(lic => lic.status === 'expired').length,
    totalRequests: window.demoData.activationRequests.length,
    totalLicenses: window.demoData.licenses.length
};

console.log('📊 تم تحميل البيانات التجريبية:', window.demoData.stats);
