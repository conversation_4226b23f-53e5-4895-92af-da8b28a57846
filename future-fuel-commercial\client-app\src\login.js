/**
 * واجهة تسجيل الدخول - مؤسسة وقود المستقبل
 * Login Interface - Future Fuel Corporation
 * 
 * يدير عملية تسجيل الدخول وطلبات التفعيل
 */

class LoginManager {
    constructor() {
        this.isLoading = false;
        this.licenseExpanded = false;
        this.initializeElements();
        this.attachEventListeners();
        this.checkExistingSession();
    }

    /**
     * تهيئة العناصر
     */
    initializeElements() {
        this.elements = {
            form: document.getElementById('login-form'),
            username: document.getElementById('username'),
            password: document.getElementById('password'),
            licenseKey: document.getElementById('license-key'),
            loginBtn: document.getElementById('login-btn'),
            licenseSection: document.getElementById('license-section'),
            licenseToggle: document.getElementById('license-toggle'),
            licenseInput: document.getElementById('license-input'),
            activationRequest: document.getElementById('activation-request'),
            alertContainer: document.getElementById('alert-container'),
            helpLink: document.getElementById('help-link'),
            supportLink: document.getElementById('support-link'),
            aboutLink: document.getElementById('about-link')
        };
    }

    /**
     * ربط مستمعي الأحداث
     */
    attachEventListeners() {
        // نموذج تسجيل الدخول
        this.elements.form.addEventListener('submit', (e) => this.handleLogin(e));

        // تبديل قسم الترخيص
        this.elements.licenseToggle.addEventListener('click', () => this.toggleLicenseSection());

        // تغيير حقل الترخيص
        this.elements.licenseKey.addEventListener('input', () => this.handleLicenseInput());

        // روابط المساعدة
        this.elements.helpLink.addEventListener('click', (e) => this.showHelp(e));
        this.elements.supportLink.addEventListener('click', (e) => this.showSupport(e));
        this.elements.aboutLink.addEventListener('click', (e) => this.showAbout(e));

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    /**
     * التحقق من الجلسة الموجودة
     */
    async checkExistingSession() {
        try {
            if (window.electronAPI && window.electronAPI.auth) {
                const sessionCheck = await window.electronAPI.auth.checkSession();
                if (sessionCheck.isValid) {
                    this.showAlert('تم العثور على جلسة نشطة، جاري تحميل التطبيق...', 'info');
                    // سيتم إعادة توجيه تلقائياً من main.js
                }
            }
        } catch (error) {
            console.error('خطأ في التحقق من الجلسة:', error);
        }
    }

    /**
     * معالجة تسجيل الدخول
     */
    async handleLogin(event) {
        event.preventDefault();
        
        if (this.isLoading) return;

        const credentials = this.getCredentials();
        if (!this.validateCredentials(credentials)) return;

        this.setLoading(true);
        this.clearAlerts();

        try {
            const result = await this.performLogin(credentials);
            this.handleLoginResult(result);
        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            this.showAlert('حدث خطأ في النظام. يرجى المحاولة لاحقاً.', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * الحصول على بيانات الاعتماد
     */
    getCredentials() {
        return {
            username: this.elements.username.value.trim(),
            password: this.elements.password.value,
            licenseKey: this.elements.licenseKey.value.trim() || null
        };
    }

    /**
     * التحقق من صحة البيانات
     */
    validateCredentials(credentials) {
        if (!credentials.username) {
            this.showAlert('يرجى إدخال اسم المستخدم', 'error');
            this.elements.username.focus();
            return false;
        }

        if (!credentials.password) {
            this.showAlert('يرجى إدخال كلمة المرور', 'error');
            this.elements.password.focus();
            return false;
        }

        if (credentials.username.length < 3) {
            this.showAlert('اسم المستخدم يجب أن يكون 3 أحرف على الأقل', 'error');
            this.elements.username.focus();
            return false;
        }

        if (credentials.password.length < 6) {
            this.showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
            this.elements.password.focus();
            return false;
        }

        return true;
    }

    /**
     * تنفيذ عملية تسجيل الدخول
     */
    async performLogin(credentials) {
        if (window.electronAPI && window.electronAPI.auth) {
            return await window.electronAPI.auth.login(credentials);
        } else {
            // محاكاة للاختبار في المتصفح
            return this.simulateLogin(credentials);
        }
    }

    /**
     * محاكاة تسجيل الدخول للاختبار
     */
    async simulateLogin(credentials) {
        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 2000));

        // محاكاة نتائج مختلفة
        if (credentials.username === 'admin' && credentials.password === 'admin123') {
            if (credentials.licenseKey && credentials.licenseKey.startsWith('FL-')) {
                return {
                    success: true,
                    message: 'تم تسجيل الدخول بنجاح',
                    user: { username: 'admin', name: 'مدير النظام' }
                };
            } else {
                return {
                    success: false,
                    message: 'تم إرسال طلب التفعيل للمطور. سيتم التواصل معك قريباً.',
                    requiresActivation: true,
                    activationRequestId: 'REQ-' + Math.random().toString(36).substr(2, 8).toUpperCase()
                };
            }
        } else {
            return {
                success: false,
                message: 'اسم المستخدم أو كلمة المرور غير صحيحة',
                requiresActivation: false
            };
        }
    }

    /**
     * معالجة نتيجة تسجيل الدخول
     */
    handleLoginResult(result) {
        if (result.success) {
            this.showAlert('تم تسجيل الدخول بنجاح! جاري تحميل التطبيق...', 'success');
            // سيتم إعادة التوجيه تلقائياً من main.js
        } else if (result.requiresActivation) {
            this.showAlert(result.message, 'warning');
            this.showActivationInfo(result.activationRequestId);
        } else {
            this.showAlert(result.message, 'error');
            this.clearForm();
        }
    }

    /**
     * إظهار معلومات التفعيل
     */
    showActivationInfo(requestId) {
        if (requestId) {
            const activationInfo = `
                <div class="alert alert-info show">
                    <h4><i class="fas fa-clock"></i> طلب التفعيل قيد المعالجة</h4>
                    <p><strong>رقم الطلب:</strong> ${requestId}</p>
                    <p>تم إرسال طلب التفعيل للمطور. سيتم التواصل معك خلال 24 ساعة لإرسال رقم الترخيص.</p>
                    <p><small>احتفظ برقم الطلب للمراجعة.</small></p>
                </div>
            `;
            this.elements.alertContainer.innerHTML = activationInfo;
        }
    }

    /**
     * تبديل قسم الترخيص
     */
    toggleLicenseSection() {
        this.licenseExpanded = !this.licenseExpanded;
        
        if (this.licenseExpanded) {
            this.elements.licenseSection.classList.add('active');
            this.elements.licenseToggle.classList.add('active');
            this.elements.licenseInput.classList.add('show');
            this.elements.activationRequest.classList.add('show');
        } else {
            this.elements.licenseSection.classList.remove('active');
            this.elements.licenseToggle.classList.remove('active');
            this.elements.licenseInput.classList.remove('show');
            this.elements.activationRequest.classList.remove('show');
        }
    }

    /**
     * معالجة إدخال الترخيص
     */
    handleLicenseInput() {
        const licenseValue = this.elements.licenseKey.value.trim();
        
        if (licenseValue) {
            this.elements.activationRequest.classList.remove('show');
        } else {
            this.elements.activationRequest.classList.add('show');
        }
    }

    /**
     * تعيين حالة التحميل
     */
    setLoading(loading) {
        this.isLoading = loading;
        
        if (loading) {
            this.elements.loginBtn.disabled = true;
            this.elements.loginBtn.classList.add('loading');
            this.elements.loginBtn.innerHTML = `
                <i class="fas fa-spinner spinner"></i>
                جاري تسجيل الدخول...
            `;
        } else {
            this.elements.loginBtn.disabled = false;
            this.elements.loginBtn.classList.remove('loading');
            this.elements.loginBtn.innerHTML = `
                <i class="fas fa-sign-in-alt" style="margin-left: 8px;"></i>
                تسجيل الدخول
            `;
        }
    }

    /**
     * إظهار تنبيه
     */
    showAlert(message, type = 'info') {
        const alertClass = `alert-${type}`;
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        const alertHTML = `
            <div class="alert ${alertClass} show">
                <i class="${icons[type]}"></i>
                ${message}
            </div>
        `;

        this.elements.alertContainer.innerHTML = alertHTML;

        // إخفاء التنبيه تلقائياً بعد 5 ثوان للرسائل العادية
        if (type === 'success' || type === 'info') {
            setTimeout(() => this.clearAlerts(), 5000);
        }
    }

    /**
     * مسح التنبيهات
     */
    clearAlerts() {
        this.elements.alertContainer.innerHTML = '';
    }

    /**
     * مسح النموذج
     */
    clearForm() {
        this.elements.password.value = '';
        this.elements.password.focus();
    }

    /**
     * معالجة اختصارات لوحة المفاتيح
     */
    handleKeyboardShortcuts(event) {
        // Enter للتسجيل
        if (event.key === 'Enter' && !this.isLoading) {
            if (document.activeElement === this.elements.username || 
                document.activeElement === this.elements.password ||
                document.activeElement === this.elements.licenseKey) {
                this.elements.form.dispatchEvent(new Event('submit'));
            }
        }

        // Escape لمسح التنبيهات
        if (event.key === 'Escape') {
            this.clearAlerts();
        }

        // F1 للمساعدة
        if (event.key === 'F1') {
            event.preventDefault();
            this.showHelp();
        }
    }

    /**
     * إظهار المساعدة
     */
    showHelp(event) {
        if (event) event.preventDefault();
        
        this.showAlert(`
            <strong>مساعدة تسجيل الدخول:</strong><br>
            • استخدم اسم المستخدم وكلمة المرور المقدمة من المطور<br>
            • إذا لم يكن لديك ترخيص، سيتم إرسال طلب تفعيل تلقائياً<br>
            • للمساعدة الفورية: اضغط F1
        `, 'info');
    }

    /**
     * إظهار الدعم التقني
     */
    showSupport(event) {
        if (event) event.preventDefault();
        
        this.showAlert(`
            <strong>الدعم التقني:</strong><br>
            📧 البريد الإلكتروني: <EMAIL><br>
            📞 الهاتف: +966-XX-XXX-XXXX<br>
            🌐 الموقع: www.futurefuel.sa
        `, 'info');
    }

    /**
     * إظهار معلومات التطبيق
     */
    showAbout(event) {
        if (event) event.preventDefault();
        
        this.showAlert(`
            <strong>مؤسسة وقود المستقبل</strong><br>
            الإصدار: 3.0.0 - النسخة التجارية<br>
            نظام إدارة شامل للمؤسسات والمحطات<br>
            © 2024 جميع الحقوق محفوظة
        `, 'info');
    }
}

// تهيئة مدير تسجيل الدخول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new LoginManager();
    console.log('✅ تم تحميل واجهة تسجيل الدخول بنجاح');
});
