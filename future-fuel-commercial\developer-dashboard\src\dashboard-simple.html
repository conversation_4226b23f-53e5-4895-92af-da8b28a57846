<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المطور - مؤسسة وقود المستقبل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            direction: rtl;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #ffd700;
        }

        .card p {
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
            transform: scale(1.05);
        }

        .status-bar {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feature-list li:before {
            content: "✓";
            color: #4CAF50;
            font-weight: bold;
            margin-left: 10px;
        }

        .footer {
            text-align: center;
            padding: 30px;
            margin-top: 50px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 0.7;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏢 لوحة تحكم المطور</h1>
        <p>مؤسسة وقود المستقبل - نظام إدارة التراخيص</p>
    </div>

    <div class="container">
        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator"></div>
                <span>النظام متصل</span>
            </div>
            <div class="status-item">
                <span id="current-time">جاري التحميل...</span>
            </div>
            <div class="status-item">
                <span>الإصدار 3.0.0</span>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <h3>📋 إدارة التراخيص</h3>
                <p>إنشاء وإدارة تراخيص العملاء مع إمكانية التحكم الكامل في الصلاحيات والمدة الزمنية.</p>
                <ul class="feature-list">
                    <li>إنشاء تراخيص مخصصة</li>
                    <li>مراقبة حالة التراخيص</li>
                    <li>تجديد وإلغاء التراخيص</li>
                </ul>
                <button class="btn" onclick="showMessage('ميزة إدارة التراخيص')">إدارة التراخيص</button>
            </div>

            <div class="card">
                <h3>🔔 طلبات التفعيل</h3>
                <p>استقبال ومعالجة طلبات التفعيل من العملاء في الوقت الفعلي.</p>
                <ul class="feature-list">
                    <li>استقبال الطلبات تلقائياً</li>
                    <li>مراجعة معلومات العميل</li>
                    <li>الموافقة أو الرفض</li>
                </ul>
                <button class="btn" onclick="showMessage('طلبات التفعيل')">عرض الطلبات</button>
            </div>

            <div class="card">
                <h3>👥 إدارة العملاء</h3>
                <p>مراقبة العملاء النشطين وإدارة حساباتهم وجلساتهم.</p>
                <ul class="feature-list">
                    <li>قائمة العملاء النشطين</li>
                    <li>تفاصيل الاستخدام</li>
                    <li>إدارة الجلسات</li>
                </ul>
                <button class="btn" onclick="showMessage('إدارة العملاء')">إدارة العملاء</button>
            </div>

            <div class="card">
                <h3>📊 الإحصائيات</h3>
                <p>عرض إحصائيات شاملة عن استخدام النظام والعملاء.</p>
                <ul class="feature-list">
                    <li>إحصائيات الاستخدام</li>
                    <li>تقارير مفصلة</li>
                    <li>رسوم بيانية تفاعلية</li>
                </ul>
                <button class="btn" onclick="showMessage('الإحصائيات')">عرض الإحصائيات</button>
            </div>

            <div class="card">
                <h3>📝 السجلات</h3>
                <p>مراجعة سجلات النظام وتتبع جميع العمليات والأنشطة.</p>
                <ul class="feature-list">
                    <li>سجلات النظام</li>
                    <li>سجلات الأمان</li>
                    <li>تصدير السجلات</li>
                </ul>
                <button class="btn" onclick="showMessage('السجلات')">عرض السجلات</button>
            </div>

            <div class="card">
                <h3>⚙️ الإعدادات</h3>
                <p>تكوين إعدادات النظام والأمان والخادم.</p>
                <ul class="feature-list">
                    <li>إعدادات الخادم</li>
                    <li>إعدادات الأمان</li>
                    <li>النسخ الاحتياطي</li>
                </ul>
                <button class="btn" onclick="showMessage('الإعدادات')">الإعدادات</button>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>© 2024 مؤسسة وقود المستقبل. جميع الحقوق محفوظة.</p>
        <p>نظام إدارة التراخيص التجاري - الإصدار 3.0.0</p>
    </div>

    <script>
        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }

        // عرض رسالة
        function showMessage(feature) {
            alert(`مرحباً بك في ${feature}\n\nهذه نسخة تجريبية من لوحة تحكم المطور.\nجميع الميزات ستكون متاحة في النسخة الكاملة.`);
        }

        // تشغيل التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
            
            // رسالة ترحيب
            setTimeout(() => {
                console.log('🚀 مرحباً بك في لوحة تحكم المطور - مؤسسة وقود المستقبل');
                console.log('📋 النظام جاهز للاستخدام');
            }, 1000);
        });
    </script>
</body>
</html>
