@echo off
echo ========================================
echo    نظام إدارة التراخيص
echo    مؤسسة وقود المستقبل
echo ========================================
echo.

echo جاري التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    echo يمكنك تحميله من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js متوفر

echo.
echo جاري التحقق من التبعيات...
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
) else (
    echo ✅ التبعيات متوفرة
)

echo.
echo 🚀 بدء خادم إدارة التراخيص...
echo.
echo المنفذ: 3000
echo لوحة التحكم: license-dashboard.html
echo للإيقاف: اضغط Ctrl+C
echo.
echo ========================================

node license-server.js

echo.
echo تم إيقاف الخادم
pause
