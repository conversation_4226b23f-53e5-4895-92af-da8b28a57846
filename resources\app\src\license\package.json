{"name": "future-fuel-license-server", "version": "1.0.0", "description": "خادم إدارة التراخيص لمؤسسة وقود المستقبل", "main": "license-server.js", "scripts": {"start": "node license-server.js", "dev": "nodemon license-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["license", "management", "gas-station", "fuel", "arabic"], "author": "مؤسسة وقود المستقبل", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}