{"name": "future-fuel-developer-dashboard", "productName": "لوحة تحكم المطور - مؤسسة وقود المستقبل", "version": "3.0.0", "description": "لوحة تحكم شاملة لإدارة التراخيص والعملاء في نظام مؤسسة وقود المستقبل", "main": "main-simple.js", "author": {"name": "مؤسسة وقود المستقبل - فريق التطوير", "email": "<EMAIL>", "url": "https://futurefuel.sa"}, "license": "Commercial", "private": true, "homepage": "https://futurefuel.sa/developer", "repository": {"type": "git", "url": "https://github.com/futurefuel/developer-dashboard.git"}, "keywords": ["license-management", "developer-dashboard", "fuel-management", "electron", "arabic", "commercial", "activation-server"], "scripts": {"start": "electron .", "dev": "electron . --dev", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose", "lint": "eslint src/**/*.js main.js", "lint:fix": "eslint src/**/*.js main.js --fix", "format": "prettier --write src/**/*.js main.js", "format:check": "prettier --check src/**/*.js main.js", "build": "node build-config.js && electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "publish": "electron-builder --publish=always", "postinstall": "electron-builder install-app-deps", "clean": "rimraf dist build coverage .nyc_output", "prebuild": "npm run clean && npm run lint && npm run test", "prepack": "npm run prebuild", "predist": "npm run prebuild", "backup-db": "node scripts/backup-database.js", "restore-db": "node scripts/restore-database.js", "export-data": "node scripts/export-data.js", "import-data": "node scripts/import-data.js", "docs": "jsdoc src/**/*.js -d docs", "security-audit": "npm audit", "update-deps": "npm update", "validate": "npm run lint && npm run test && npm run security-audit"}, "dependencies": {"electron": "^28.0.0"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "rimraf": "^5.0.5", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "concurrently": "^8.2.2", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "babel-loader": "^9.1.3", "prettier": "^3.1.1", "jsdom": "^23.0.1", "jest-environment-jsdom": "^29.7.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^27.6.0", "jsdoc": "^4.0.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "file-loader": "^6.2.0", "better-sqlite3": "^9.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testMatch": ["**/__tests__/**/*.js", "**/?(*.)+(spec|test).js"], "collectCoverageFrom": ["src/**/*.js", "main.js", "!src/**/*.test.js", "!tests/**/*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1", "^@shared/(.*)$": "<rootDir>/../shared/$1"}, "testTimeout": 10000, "verbose": true}, "build": {"appId": "sa.futurefuel.developer", "productName": "لوحة تحكم المطور - مؤسسة وقود المستقبل", "copyright": "Copyright © 2024 مؤسسة وقود المستقبل. جميع الحقوق محفوظة.", "directories": {"output": "dist", "buildResources": "build"}, "files": ["main.js", "preload.js", "src/**/*", "assets/**/*", "scripts/**/*", "data/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "../shared", "to": "shared", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icons/developer-icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}", "publisherName": "مؤسسة وقود المستقبل"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "assets/icons/installer.ico", "uninstallerIcon": "assets/icons/uninstaller.ico", "installerHeaderIcon": "assets/icons/header.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "لوحة تحكم المطور", "include": "build/installer.nsh", "artifactName": "${productName}-Setup-${version}.${ext}", "deleteAppDataOnUninstall": false, "menuCategory": "Development", "runAfterFinish": true, "installerLanguages": ["ar", "en"], "language": "ar"}, "portable": {"artifactName": "${productName}-Portable-${version}.${ext}"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icons/developer-icon.icns", "category": "public.app-category.developer-tools", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "dmg": {"title": "${productName} ${version}", "icon": "assets/icons/volume.icns", "background": "assets/dmg-background.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icons/developer-icon.png", "category": "Development", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "publish": {"provider": "github", "owner": "futurefuel", "repo": "developer-dashboard", "private": true, "releaseType": "release"}, "compression": "maximum", "removePackageScripts": true, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "electronDownload": {"cache": ".electron-cache"}}, "electronDownload": {"mirror": "https://npm.taobao.org/mirrors/electron/"}}