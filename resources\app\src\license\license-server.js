/**
 * خادم إدارة التراخيص
 * License Management Server
 */

const express = require('express');
const cors = require('cors');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

class LicenseServer {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.dataDir = path.join(__dirname, 'data');
        this.requestsFile = path.join(this.dataDir, 'activation-requests.json');
        this.licensesFile = path.join(this.dataDir, 'licenses.json');
        this.settingsFile = path.join(this.dataDir, 'settings.json');
        
        this.init();
    }

    async init() {
        console.log('🚀 تهيئة خادم إدارة التراخيص...');
        
        // إنشاء مجلد البيانات
        await this.ensureDataDirectory();
        
        // تهيئة Express
        this.setupMiddleware();
        this.setupRoutes();
        
        // بدء الخادم
        this.start();
    }

    async ensureDataDirectory() {
        try {
            await fs.access(this.dataDir);
        } catch {
            await fs.mkdir(this.dataDir, { recursive: true });
            console.log('📁 تم إنشاء مجلد البيانات');
        }

        // إنشاء ملفات البيانات الأولية إذا لم تكن موجودة
        await this.initializeDataFiles();
    }

    async initializeDataFiles() {
        const files = [
            { path: this.requestsFile, data: [] },
            { path: this.licensesFile, data: [] },
            { 
                path: this.settingsFile, 
                data: {
                    serverPort: 3000,
                    autoBackup: true,
                    defaultDuration: 365,
                    autoApprove: false,
                    encryptionKey: crypto.randomBytes(32).toString('hex')
                }
            }
        ];

        for (const file of files) {
            try {
                await fs.access(file.path);
            } catch {
                await fs.writeFile(file.path, JSON.stringify(file.data, null, 2));
                console.log(`📄 تم إنشاء ملف: ${path.basename(file.path)}`);
            }
        }
    }

    setupMiddleware() {
        // CORS للسماح بالطلبات من التطبيق
        this.app.use(cors({
            origin: ['http://localhost:3000', 'file://', 'app://'],
            credentials: true
        }));

        // تحليل JSON
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true }));

        // تسجيل الطلبات
        this.app.use((req, res, next) => {
            console.log(`📨 ${req.method} ${req.path} - ${new Date().toISOString()}`);
            next();
        });

        // معالجة الأخطاء
        this.app.use((err, req, res, next) => {
            console.error('❌ خطأ في الخادم:', err);
            res.status(500).json({
                success: false,
                error: 'حدث خطأ داخلي في الخادم'
            });
        });
    }

    setupRoutes() {
        // الصفحة الرئيسية
        this.app.get('/', (req, res) => {
            res.json({
                message: 'خادم إدارة التراخيص - مؤسسة وقود المستقبل',
                version: '1.0.0',
                status: 'running',
                timestamp: new Date().toISOString()
            });
        });

        // طلبات التفعيل
        this.app.post('/api/activation-request', this.handleActivationRequest.bind(this));
        this.app.get('/api/activation-requests', this.getActivationRequests.bind(this));
        this.app.patch('/api/activation-requests/:id', this.updateActivationRequest.bind(this));

        // التراخيص
        this.app.get('/api/licenses', this.getLicenses.bind(this));
        this.app.post('/api/licenses', this.createLicense.bind(this));
        this.app.patch('/api/licenses/:id', this.updateLicense.bind(this));
        this.app.delete('/api/licenses/:id', this.deleteLicense.bind(this));

        // التحقق من الترخيص
        this.app.post('/api/validate-license', this.validateLicense.bind(this));

        // الإعدادات
        this.app.get('/api/settings', this.getSettings.bind(this));
        this.app.post('/api/settings', this.updateSettings.bind(this));

        // معلومات النظام
        this.app.get('/api/system-info', this.getSystemInfo.bind(this));
    }

    // معالج طلبات التفعيل من واجهة تسجيل الدخول
    async handleActivationRequest(req, res) {
        try {
            const { companyName, contactName, contactPhone, contactEmail, deviceId, systemInfo } = req.body;

            // التحقق من البيانات المطلوبة
            if (!companyName || !contactName || !contactPhone || !deviceId) {
                return res.status(400).json({
                    success: false,
                    error: 'البيانات المطلوبة غير مكتملة'
                });
            }

            // قراءة الطلبات الحالية
            const requests = await this.readDataFile(this.requestsFile);

            // التحقق من عدم وجود طلب مكرر لنفس الجهاز
            const existingRequest = requests.find(r => r.deviceId === deviceId && r.status === 'pending');
            if (existingRequest) {
                return res.status(409).json({
                    success: false,
                    error: 'يوجد طلب تفعيل معلق لهذا الجهاز بالفعل'
                });
            }

            // إنشاء طلب تفعيل جديد
            const request = {
                id: this.generateId(),
                companyName,
                contactName,
                contactPhone,
                contactEmail: contactEmail || null,
                deviceId,
                systemInfo: systemInfo || null,
                status: 'pending',
                date: new Date().toISOString(),
                ipAddress: req.ip || req.connection.remoteAddress
            };

            // إضافة الطلب
            requests.push(request);
            await this.writeDataFile(this.requestsFile, requests);

            console.log(`✅ تم استلام طلب تفعيل جديد من: ${companyName} (${deviceId})`);

            res.json({
                success: true,
                message: 'تم إرسال طلب التفعيل بنجاح',
                requestId: request.id
            });

        } catch (error) {
            console.error('❌ خطأ في معالجة طلب التفعيل:', error);
            res.status(500).json({
                success: false,
                error: 'حدث خطأ أثناء معالجة طلب التفعيل'
            });
        }
    }

    // الحصول على طلبات التفعيل
    async getActivationRequests(req, res) {
        try {
            const requests = await this.readDataFile(this.requestsFile);
            res.json({
                success: true,
                data: requests.sort((a, b) => new Date(b.date) - new Date(a.date))
            });
        } catch (error) {
            console.error('❌ خطأ في قراءة طلبات التفعيل:', error);
            res.status(500).json({
                success: false,
                error: 'حدث خطأ أثناء قراءة طلبات التفعيل'
            });
        }
    }

    // تحديث طلب التفعيل
    async updateActivationRequest(req, res) {
        try {
            const { id } = req.params;
            const { status, notes } = req.body;

            const requests = await this.readDataFile(this.requestsFile);
            const requestIndex = requests.findIndex(r => r.id === id);

            if (requestIndex === -1) {
                return res.status(404).json({
                    success: false,
                    error: 'طلب التفعيل غير موجود'
                });
            }

            requests[requestIndex].status = status;
            requests[requestIndex].notes = notes;
            requests[requestIndex].updatedAt = new Date().toISOString();

            await this.writeDataFile(this.requestsFile, requests);

            res.json({
                success: true,
                message: 'تم تحديث طلب التفعيل بنجاح'
            });

        } catch (error) {
            console.error('❌ خطأ في تحديث طلب التفعيل:', error);
            res.status(500).json({
                success: false,
                error: 'حدث خطأ أثناء تحديث طلب التفعيل'
            });
        }
    }

    // الحصول على التراخيص
    async getLicenses(req, res) {
        try {
            const licenses = await this.readDataFile(this.licensesFile);
            res.json({
                success: true,
                data: licenses.sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate))
            });
        } catch (error) {
            console.error('❌ خطأ في قراءة التراخيص:', error);
            res.status(500).json({
                success: false,
                error: 'حدث خطأ أثناء قراءة التراخيص'
            });
        }
    }

    // إنشاء ترخيص جديد
    async createLicense(req, res) {
        try {
            const licenseData = req.body;
            
            // التحقق من البيانات المطلوبة
            if (!licenseData.companyName || !licenseData.deviceId) {
                return res.status(400).json({
                    success: false,
                    error: 'البيانات المطلوبة غير مكتملة'
                });
            }

            const licenses = await this.readDataFile(this.licensesFile);

            // إنشاء الترخيص
            const license = {
                id: this.generateId(),
                licenseNumber: this.generateLicenseNumber(licenses.length + 1),
                ...licenseData,
                createdDate: new Date().toISOString(),
                status: 'active'
            };

            licenses.push(license);
            await this.writeDataFile(this.licensesFile, licenses);

            console.log(`✅ تم إنشاء ترخيص جديد: ${license.licenseNumber}`);

            res.json({
                success: true,
                message: 'تم إنشاء الترخيص بنجاح',
                license
            });

        } catch (error) {
            console.error('❌ خطأ في إنشاء الترخيص:', error);
            res.status(500).json({
                success: false,
                error: 'حدث خطأ أثناء إنشاء الترخيص'
            });
        }
    }

    // التحقق من صحة الترخيص
    async validateLicense(req, res) {
        try {
            const { deviceId, licenseNumber } = req.body;

            if (!deviceId) {
                return res.status(400).json({
                    success: false,
                    error: 'معرف الجهاز مطلوب'
                });
            }

            const licenses = await this.readDataFile(this.licensesFile);
            const license = licenses.find(l => 
                l.deviceId === deviceId && 
                (licenseNumber ? l.licenseNumber === licenseNumber : true)
            );

            if (!license) {
                return res.json({
                    success: false,
                    valid: false,
                    error: 'الترخيص غير موجود'
                });
            }

            // التحقق من انتهاء الصلاحية
            const now = new Date();
            const expiryDate = new Date(license.expiryDate);
            const isExpired = now > expiryDate;

            // التحقق من حالة الترخيص
            const isActive = license.status === 'active' && !isExpired;

            res.json({
                success: true,
                valid: isActive,
                license: {
                    licenseNumber: license.licenseNumber,
                    companyName: license.companyName,
                    expiryDate: license.expiryDate,
                    status: license.status,
                    type: license.type,
                    isExpired,
                    daysRemaining: isExpired ? 0 : Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24))
                }
            });

        } catch (error) {
            console.error('❌ خطأ في التحقق من الترخيص:', error);
            res.status(500).json({
                success: false,
                error: 'حدث خطأ أثناء التحقق من الترخيص'
            });
        }
    }

    // الأدوات المساعدة
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    generateLicenseNumber(sequence) {
        const year = new Date().getFullYear();
        const paddedSequence = String(sequence).padStart(3, '0');
        return `LIC-${year}-${paddedSequence}`;
    }

    async readDataFile(filePath) {
        try {
            const data = await fs.readFile(filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error);
            return [];
        }
    }

    async writeDataFile(filePath, data) {
        try {
            await fs.writeFile(filePath, JSON.stringify(data, null, 2));
        } catch (error) {
            console.error(`❌ خطأ في كتابة الملف ${filePath}:`, error);
            throw error;
        }
    }

    start() {
        this.app.listen(this.port, () => {
            console.log(`🌐 خادم إدارة التراخيص يعمل على المنفذ ${this.port}`);
            console.log(`📊 لوحة التحكم: http://localhost:${this.port}`);
            console.log(`🔗 API: http://localhost:${this.port}/api`);
        });
    }
}

// بدء الخادم
if (require.main === module) {
    new LicenseServer();
}

module.exports = LicenseServer;
