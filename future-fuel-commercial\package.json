{"name": "future-fuel-api-server", "version": "3.0.0", "description": "خادم API لنظام مؤسسة وقود المستقبل", "main": "api-server.js", "scripts": {"start": "node api-server.js", "dev": "nodemon api-server.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["api", "server", "fuel", "management", "license"], "author": "مؤسسة وقود المستقبل", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.2"}}