/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */


/// <reference no-default-lib="true"/>

interface Int8ArrayConstructor {
    new (): Int8Array<ArrayBuffer>;
}

interface Uint8ArrayConstructor {
    new (): Uint8Array<ArrayBuffer>;
}

interface Uint8ClampedArrayConstructor {
    new (): Uint8ClampedArray<ArrayBuffer>;
}

interface Int16ArrayConstructor {
    new (): Int16Array<ArrayBuffer>;
}

interface Uint16ArrayConstructor {
    new (): Uint16Array<ArrayBuffer>;
}

interface Int32ArrayConstructor {
    new (): Int32Array<ArrayBuffer>;
}

interface Uint32ArrayConstructor {
    new (): Uint32Array<ArrayBuffer>;
}

interface Float32ArrayConstructor {
    new (): Float32Array<ArrayBuffer>;
}

interface Float64ArrayConstructor {
    new (): Float64Array<ArrayBuffer>;
}
