/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* الألوان الأساسية */
    --primary-color: #2196F3;
    --primary-dark: #1976D2;
    --primary-light: #BBDEFB;
    --secondary-color: #FF9800;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --error-color: #F44336;
    --info-color: #2196F3;
    
    /* الألوان المحايدة */
    --background-color: #F5F5F5;
    --surface-color: #FFFFFF;
    --card-color: #FFFFFF;
    --border-color: #E0E0E0;
    --text-primary: #212121;
    --text-secondary: #757575;
    --text-disabled: #BDBDBD;
    
    /* الظلال */
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
    
    /* المسافات */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* الخطوط */
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    --font-size-xxl: 32px;
    
    /* الانتقالات */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* أبعاد الشريط الجانبي والعلوي */
    --sidebar-width: 280px;
    --navbar-height: 70px;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
    overflow-x: hidden;
}

/* شريط التنقل العلوي */
.top-navbar {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    height: var(--navbar-height);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-medium);
    z-index: 1000;
}

.navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 var(--spacing-lg);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.brand-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}

.navbar-brand h1 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin: 0;
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.action-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.server-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    font-size: var(--font-size-sm);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.status-indicator.offline {
    background: var(--error-color);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: var(--navbar-height);
    right: 0;
    width: var(--sidebar-width);
    height: calc(100vh - var(--navbar-height));
    background: var(--surface-color);
    border-left: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
    z-index: 900;
    overflow-y: auto;
}

.sidebar-nav {
    padding: var(--spacing-lg) 0;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition-fast);
    position: relative;
    color: var(--text-secondary);
}

.nav-item:hover {
    background: var(--primary-light);
    color: var(--primary-dark);
}

.nav-item.active {
    background: var(--primary-color);
    color: white;
}

.nav-item.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--secondary-color);
}

.nav-item .material-icons {
    font-size: 24px;
}

.nav-text {
    font-weight: 500;
    flex: 1;
}

.badge {
    background: var(--error-color);
    color: white;
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    margin-right: var(--sidebar-width);
    margin-top: var(--navbar-height);
    padding: var(--spacing-xl);
    min-height: calc(100vh - var(--navbar-height));
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-header {
    margin-bottom: var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.section-header h2 {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.section-header p {
    color: var(--text-secondary);
    font-size: var(--font-size-md);
}

/* الأزرار */
.primary-btn, .secondary-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: 8px;
    font-family: var(--font-family);
    font-size: var(--font-size-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
}

.primary-btn {
    background: var(--primary-color);
    color: white;
}

.primary-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.secondary-btn {
    background: var(--surface-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.secondary-btn:hover {
    background: var(--background-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--card-color);
    padding: var(--spacing-lg);
    border-radius: 12px;
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-light);
    color: var(--primary-color);
}

.stat-icon.active {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.stat-icon.pending {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.stat-icon.online {
    background: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
}

.stat-icon .material-icons {
    font-size: 28px;
}

.stat-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-content p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* شبكة لوحة المعلومات */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.dashboard-card {
    background: var(--card-color);
    border-radius: 12px;
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--background-color);
}

.card-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.card-content {
    padding: var(--spacing-lg);
}

/* قائمة النشاط */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-light);
    color: var(--primary-color);
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.activity-time {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* مرشحات الطلبات */
.requests-filters {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    background: var(--surface-color);
    color: var(--text-secondary);
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* قائمة الطلبات */
.requests-list {
    display: grid;
    gap: var(--spacing-md);
}

.request-card {
    background: var(--card-color);
    border-radius: 12px;
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    border-right: 4px solid var(--warning-color);
    transition: var(--transition-fast);
}

.request-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.request-card.approved {
    border-right-color: var(--success-color);
}

.request-card.rejected {
    border-right-color: var(--error-color);
}

.request-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.request-info h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.request-info p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.request-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
}

.request-status.pending {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.request-status.approved {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.request-status.rejected {
    background: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
}

.request-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.detail-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.detail-value {
    font-size: var(--font-size-md);
    color: var(--text-primary);
}

.request-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

.request-actions .primary-btn,
.request-actions .secondary-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

/* النوافذ المنبثقة */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(4px);
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--surface-color);
    border-radius: 12px;
    box-shadow: var(--shadow-heavy);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 50%;
    transition: var(--transition-fast);
}

.close-btn:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

.modal-content {
    padding: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: var(--font-family);
    font-size: var(--font-size-md);
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.modal-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
}

/* رسائل التنبيه */
.notifications-container {
    position: fixed;
    top: calc(var(--navbar-height) + var(--spacing-md));
    left: var(--spacing-md);
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.notification {
    background: var(--surface-color);
    border-radius: 8px;
    padding: var(--spacing-md);
    box-shadow: var(--shadow-medium);
    border-right: 4px solid var(--info-color);
    min-width: 300px;
    animation: slideInLeft 0.3s ease;
}

.notification.success {
    border-right-color: var(--success-color);
}

.notification.warning {
    border-right-color: var(--warning-color);
}

.notification.error {
    border-right-color: var(--error-color);
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* التصميم المتجاوب */
@media (max-width: 1024px) {
    :root {
        --sidebar-width: 250px;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform var(--transition-medium);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
        padding: var(--spacing-md);
    }
    
    .section-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .navbar-brand h1 {
        display: none;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .request-details {
        grid-template-columns: 1fr;
    }
    
    .request-actions {
        justify-content: stretch;
    }
    
    .request-actions .primary-btn,
    .request-actions .secondary-btn {
        flex: 1;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .modal {
        width: 95%;
        margin: var(--spacing-md);
    }
    
    .notifications-container {
        left: var(--spacing-sm);
        right: var(--spacing-sm);
    }
    
    .notification {
        min-width: auto;
    }
}
