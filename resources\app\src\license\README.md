# نظام إدارة التراخيص - مؤسسة وقود المستقبل

## نظرة عامة

نظام إدارة التراخيص هو جزء من تطبيق إدارة محطات الوقود، يوفر:

- **استقبال طلبات التفعيل** من واجهة تسجيل الدخول
- **لوحة تحكم شاملة** لإدارة التراخيص
- **التحقق من صحة التراخيص** في الوقت الفعلي
- **إدارة دورة حياة الترخيص** الكاملة

## المكونات

### 1. خادم التراخيص (`license-server.js`)
- خادم Express.js لاستقبال ومعالجة طلبات التفعيل
- API endpoints لإدارة التراخيص
- تخزين البيانات في ملفات JSON
- التحقق من صحة التراخيص

### 2. لوحة التحكم (`license-dashboard.html/css/js`)
- واجهة ويب شاملة لإدارة التراخيص
- إحصائيات في الوقت الفعلي
- إدارة طلبات التفعيل (موافقة/رفض)
- إنشاء وتمديد وإلغاء التراخيص
- تصدير البيانات وإعدادات النظام

### 3. تكامل مع نظام تسجيل الدخول
- إرسال طلبات التفعيل تلقائياً
- حفظ الطلبات محلياً في حالة عدم توفر الاتصال
- إعادة المحاولة التلقائية

## التثبيت والتشغيل

### 1. تثبيت التبعيات

```bash
cd resources/app/src/license
npm install
```

### 2. تشغيل خادم التراخيص

```bash
# تشغيل عادي
npm start

# تشغيل مع إعادة التحميل التلقائي (للتطوير)
npm run dev
```

### 3. الوصول إلى لوحة التحكم

افتح المتصفح وانتقل إلى:
```
file:///path/to/resources/app/src/license/license-dashboard.html
```

## API Endpoints

### طلبات التفعيل

- `POST /api/activation-request` - إرسال طلب تفعيل جديد
- `GET /api/activation-requests` - الحصول على جميع طلبات التفعيل
- `PATCH /api/activation-requests/:id` - تحديث حالة طلب التفعيل

### التراخيص

- `GET /api/licenses` - الحصول على جميع التراخيص
- `POST /api/licenses` - إنشاء ترخيص جديد
- `PATCH /api/licenses/:id` - تحديث ترخيص
- `DELETE /api/licenses/:id` - حذف ترخيص

### التحقق من الترخيص

- `POST /api/validate-license` - التحقق من صحة الترخيص

### الإعدادات

- `GET /api/settings` - الحصول على إعدادات النظام
- `POST /api/settings` - تحديث إعدادات النظام

## هيكل البيانات

### طلب التفعيل

```json
{
  "id": "req_001",
  "companyName": "محطة وقود الرياض",
  "contactName": "أحمد محمد",
  "contactPhone": "**********",
  "contactEmail": "<EMAIL>",
  "deviceId": "RYD-001-2024",
  "systemInfo": {...},
  "status": "pending",
  "date": "2024-01-01T00:00:00.000Z"
}
```

### الترخيص

```json
{
  "id": "lic_001",
  "licenseNumber": "LIC-2024-001",
  "companyName": "محطة النور للوقود",
  "contactName": "فاطمة علي",
  "contactPhone": "**********",
  "deviceId": "NUR-001-2024",
  "createdDate": "2024-01-01T00:00:00.000Z",
  "expiryDate": "2025-01-01T00:00:00.000Z",
  "status": "active",
  "type": "standard"
}
```

## الحالات المدعومة

### حالات طلبات التفعيل
- `pending` - معلق
- `approved` - موافق عليه
- `rejected` - مرفوض

### حالات التراخيص
- `active` - نشط
- `expired` - منتهي الصلاحية
- `revoked` - ملغي

### أنواع التراخيص
- `standard` - قياسي
- `premium` - مميز
- `enterprise` - مؤسسي

## الأمان

- جميع البيانات محفوظة محلياً في ملفات JSON
- التحقق من صحة البيانات المدخلة
- تشفير معرفات الأجهزة
- تسجيل جميع العمليات

## استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الاتصال بالخادم**
   - تأكد من تشغيل خادم التراخيص
   - تحقق من رقم المنفذ (افتراضي: 3000)

2. **عدم ظهور البيانات**
   - تحقق من وجود مجلد `data` وملفات JSON
   - راجع console للأخطاء

3. **مشاكل في واجهة المستخدم**
   - تأكد من تحميل جميع ملفات CSS و JavaScript
   - تحقق من دعم المتصفح للميزات المستخدمة

## التطوير

### إضافة ميزات جديدة

1. أضف endpoint جديد في `license-server.js`
2. أضف الواجهة في `license-dashboard.html`
3. أضف المنطق في `license-dashboard.js`
4. اختبر الميزة الجديدة

### قاعدة البيانات

حالياً يستخدم النظام ملفات JSON للتخزين. يمكن ترقيته لاستخدام:
- SQLite للتطبيقات الصغيرة
- PostgreSQL أو MySQL للتطبيقات الكبيرة
- MongoDB للبيانات غير المهيكلة

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**مؤسسة وقود المستقبل** - نظام إدارة محطات الوقود المتكامل
