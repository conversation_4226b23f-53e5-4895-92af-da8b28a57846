/**
 * مدير التراخيص - لوحة تحكم المطور
 * License Manager - Developer Dashboard
 * 
 * يدير إنشاء وتوزيع التراخيص وطلبات التفعيل
 */

const crypto = require('crypto');
const express = require('express');
const cors = require('cors');
const { machineId } = require('node-machine-id');

class LicenseManager {
    constructor(databaseManager) {
        this.db = databaseManager;
        this.apiServer = null;
        this.serverPort = 3001;
        this.encryptionKey = this.generateEncryptionKey();
        this.activationRequests = new Map();
        this.connectedClients = new Map();
    }

    /**
     * تهيئة مدير التراخيص
     */
    async initialize() {
        try {
            // إنشاء جداول قاعدة البيانات
            await this.createDatabaseTables();
            
            // تحميل طلبات التفعيل المحفوظة
            await this.loadActivationRequests();
            
            console.log('✅ تم تهيئة مدير التراخيص بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تهيئة مدير التراخيص:', error);
            throw error;
        }
    }

    /**
     * إنشاء جداول قاعدة البيانات
     */
    async createDatabaseTables() {
        const tables = [
            // جدول التراخيص
            `CREATE TABLE IF NOT EXISTS licenses (
                id TEXT PRIMARY KEY,
                license_key TEXT UNIQUE NOT NULL,
                client_name TEXT NOT NULL,
                client_email TEXT,
                client_phone TEXT,
                company_name TEXT,
                device_fingerprint TEXT,
                features TEXT,
                expiry_date TEXT,
                max_users INTEGER DEFAULT 1,
                status TEXT DEFAULT 'active',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                notes TEXT
            )`,
            
            // جدول طلبات التفعيل
            `CREATE TABLE IF NOT EXISTS activation_requests (
                id TEXT PRIMARY KEY,
                username TEXT NOT NULL,
                device_fingerprint TEXT NOT NULL,
                device_info TEXT,
                client_ip TEXT,
                request_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'pending',
                approved_by TEXT,
                approved_at TEXT,
                license_id TEXT,
                rejection_reason TEXT,
                notes TEXT
            )`,
            
            // جدول سجل الأحداث
            `CREATE TABLE IF NOT EXISTS event_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                event_type TEXT NOT NULL,
                event_data TEXT,
                user_id TEXT,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT,
                user_agent TEXT
            )`,
            
            // جدول الجلسات النشطة
            `CREATE TABLE IF NOT EXISTS active_sessions (
                session_id TEXT PRIMARY KEY,
                license_id TEXT NOT NULL,
                device_fingerprint TEXT NOT NULL,
                start_time TEXT DEFAULT CURRENT_TIMESTAMP,
                last_activity TEXT DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT,
                status TEXT DEFAULT 'active'
            )`
        ];

        for (const tableSQL of tables) {
            await this.db.run(tableSQL);
        }
    }

    /**
     * تحميل طلبات التفعيل المحفوظة
     */
    async loadActivationRequests() {
        try {
            const requests = await this.db.all(
                'SELECT * FROM activation_requests WHERE status = ?',
                ['pending']
            );

            for (const request of requests) {
                this.activationRequests.set(request.id, {
                    ...request,
                    device_info: JSON.parse(request.device_info || '{}')
                });
            }

            console.log(`📋 تم تحميل ${requests.length} طلب تفعيل معلق`);
        } catch (error) {
            console.error('خطأ في تحميل طلبات التفعيل:', error);
        }
    }

    /**
     * بدء خادم API
     */
    async startAPIServer() {
        return new Promise((resolve, reject) => {
            try {
                this.apiServer = express();
                
                // إعدادات الأمان
                this.apiServer.use(cors({
                    origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
                    credentials: true
                }));
                
                this.apiServer.use(express.json({ limit: '10mb' }));
                this.apiServer.use(express.urlencoded({ extended: true, limit: '10mb' }));

                // تسجيل الطلبات
                this.apiServer.use((req, res, next) => {
                    console.log(`📡 ${req.method} ${req.path} من ${req.ip}`);
                    next();
                });

                // نقاط النهاية API
                this.setupAPIEndpoints();

                // بدء الخادم
                this.apiServer.listen(this.serverPort, '127.0.0.1', () => {
                    console.log(`🌐 خادم التراخيص يعمل على المنفذ ${this.serverPort}`);
                    resolve();
                });

            } catch (error) {
                console.error('خطأ في بدء خادم API:', error);
                reject(error);
            }
        });
    }

    /**
     * إعداد نقاط النهاية API
     */
    setupAPIEndpoints() {
        // استقبال طلبات التفعيل
        this.apiServer.post('/api/activation-request', async (req, res) => {
            try {
                const { username, deviceFingerprint, deviceInfo } = req.body;
                
                if (!username || !deviceFingerprint) {
                    return res.status(400).json({
                        success: false,
                        message: 'بيانات غير مكتملة'
                    });
                }

                const requestId = this.generateRequestId();
                const activationRequest = {
                    id: requestId,
                    username: username,
                    device_fingerprint: deviceFingerprint,
                    device_info: JSON.stringify(deviceInfo || {}),
                    client_ip: req.ip,
                    request_timestamp: new Date().toISOString(),
                    status: 'pending'
                };

                // حفظ في قاعدة البيانات
                await this.db.run(
                    `INSERT INTO activation_requests 
                     (id, username, device_fingerprint, device_info, client_ip, status)
                     VALUES (?, ?, ?, ?, ?, ?)`,
                    [
                        activationRequest.id,
                        activationRequest.username,
                        activationRequest.device_fingerprint,
                        activationRequest.device_info,
                        activationRequest.client_ip,
                        activationRequest.status
                    ]
                );

                // حفظ في الذاكرة
                this.activationRequests.set(requestId, activationRequest);

                // تسجيل الحدث
                await this.logEvent('activation_request_received', {
                    requestId: requestId,
                    username: username,
                    deviceFingerprint: deviceFingerprint
                }, null, req.ip);

                console.log(`📨 تم استلام طلب تفعيل جديد: ${requestId} من ${username}`);

                res.json({
                    success: true,
                    message: 'تم إرسال طلب التفعيل بنجاح',
                    requestId: requestId
                });

            } catch (error) {
                console.error('خطأ في معالجة طلب التفعيل:', error);
                res.status(500).json({
                    success: false,
                    message: 'حدث خطأ في النظام'
                });
            }
        });

        // التحقق من حالة طلب التفعيل
        this.apiServer.get('/api/activation-status/:requestId', async (req, res) => {
            try {
                const { requestId } = req.params;
                
                const request = await this.db.get(
                    'SELECT * FROM activation_requests WHERE id = ?',
                    [requestId]
                );

                if (!request) {
                    return res.status(404).json({
                        success: false,
                        message: 'طلب التفعيل غير موجود'
                    });
                }

                res.json({
                    success: true,
                    status: request.status,
                    licenseKey: request.status === 'approved' ? request.license_id : null,
                    message: this.getStatusMessage(request.status)
                });

            } catch (error) {
                console.error('خطأ في التحقق من حالة التفعيل:', error);
                res.status(500).json({
                    success: false,
                    message: 'حدث خطأ في النظام'
                });
            }
        });

        // التحقق من صحة الترخيص
        this.apiServer.post('/api/validate-license', async (req, res) => {
            try {
                const { licenseKey, deviceFingerprint } = req.body;
                
                const license = await this.db.get(
                    'SELECT * FROM licenses WHERE license_key = ? AND status = ?',
                    [licenseKey, 'active']
                );

                if (!license) {
                    return res.json({
                        success: false,
                        message: 'ترخيص غير صالح'
                    });
                }

                // التحقق من انتهاء الصلاحية
                if (license.expiry_date && new Date(license.expiry_date) < new Date()) {
                    return res.json({
                        success: false,
                        message: 'انتهت صلاحية الترخيص'
                    });
                }

                // التحقق من بصمة الجهاز
                if (license.device_fingerprint && license.device_fingerprint !== deviceFingerprint) {
                    return res.json({
                        success: false,
                        message: 'الترخيص مرتبط بجهاز آخر'
                    });
                }

                // تسجيل الجلسة النشطة
                const sessionId = this.generateSessionId();
                await this.db.run(
                    `INSERT OR REPLACE INTO active_sessions 
                     (session_id, license_id, device_fingerprint, ip_address)
                     VALUES (?, ?, ?, ?)`,
                    [sessionId, license.id, deviceFingerprint, req.ip]
                );

                res.json({
                    success: true,
                    message: 'تم التحقق من الترخيص بنجاح',
                    sessionId: sessionId,
                    features: JSON.parse(license.features || '{}'),
                    expiryDate: license.expiry_date
                });

            } catch (error) {
                console.error('خطأ في التحقق من الترخيص:', error);
                res.status(500).json({
                    success: false,
                    message: 'حدث خطأ في النظام'
                });
            }
        });

        // معلومات الخادم
        this.apiServer.get('/api/server-info', (req, res) => {
            res.json({
                name: 'خادم تراخيص مؤسسة وقود المستقبل',
                version: '3.0.0',
                status: 'active',
                timestamp: new Date().toISOString()
            });
        });
    }

    /**
     * إيقاف خادم API
     */
    async stopAPIServer() {
        if (this.apiServer) {
            return new Promise((resolve) => {
                this.apiServer.close(() => {
                    console.log('🛑 تم إيقاف خادم التراخيص');
                    resolve();
                });
            });
        }
    }

    /**
     * الحصول على بيانات لوحة التحكم
     */
    async getDashboardData() {
        try {
            const [
                totalLicenses,
                activeLicenses,
                expiredLicenses,
                pendingRequests,
                activeSessions
            ] = await Promise.all([
                this.db.get('SELECT COUNT(*) as count FROM licenses'),
                this.db.get('SELECT COUNT(*) as count FROM licenses WHERE status = ?', ['active']),
                this.db.get('SELECT COUNT(*) as count FROM licenses WHERE expiry_date < datetime("now") AND status = ?', ['active']),
                this.db.get('SELECT COUNT(*) as count FROM activation_requests WHERE status = ?', ['pending']),
                this.db.get('SELECT COUNT(*) as count FROM active_sessions WHERE status = ?', ['active'])
            ]);

            return {
                statistics: {
                    totalLicenses: totalLicenses.count,
                    activeLicenses: activeLicenses.count,
                    expiredLicenses: expiredLicenses.count,
                    pendingRequests: pendingRequests.count,
                    activeSessions: activeSessions.count
                },
                recentActivity: await this.getRecentActivity(),
                serverStatus: {
                    isRunning: this.apiServer !== null,
                    port: this.serverPort,
                    uptime: process.uptime()
                }
            };
        } catch (error) {
            console.error('خطأ في الحصول على بيانات لوحة التحكم:', error);
            return null;
        }
    }

    /**
     * الحصول على النشاط الأخير
     */
    async getRecentActivity() {
        try {
            return await this.db.all(
                'SELECT * FROM event_log ORDER BY timestamp DESC LIMIT 10'
            );
        } catch (error) {
            console.error('خطأ في الحصول على النشاط الأخير:', error);
            return [];
        }
    }

    /**
     * الحصول على طلبات التفعيل
     */
    async getActivationRequests() {
        try {
            return await this.db.all(
                'SELECT * FROM activation_requests ORDER BY request_timestamp DESC'
            );
        } catch (error) {
            console.error('خطأ في الحصول على طلبات التفعيل:', error);
            return [];
        }
    }

    /**
     * الموافقة على طلب التفعيل
     */
    async approveActivationRequest(requestId, licenseData) {
        try {
            // إنشاء ترخيص جديد
            const license = await this.createLicense({
                ...licenseData,
                deviceFingerprint: licenseData.deviceFingerprint
            });

            if (!license.success) {
                return license;
            }

            // تحديث طلب التفعيل
            await this.db.run(
                `UPDATE activation_requests 
                 SET status = ?, approved_at = ?, license_id = ?
                 WHERE id = ?`,
                ['approved', new Date().toISOString(), license.licenseKey, requestId]
            );

            // إزالة من الذاكرة
            this.activationRequests.delete(requestId);

            // تسجيل الحدث
            await this.logEvent('activation_request_approved', {
                requestId: requestId,
                licenseKey: license.licenseKey
            });

            console.log(`✅ تم الموافقة على طلب التفعيل: ${requestId}`);

            return {
                success: true,
                message: 'تم الموافقة على طلب التفعيل بنجاح',
                licenseKey: license.licenseKey
            };

        } catch (error) {
            console.error('خطأ في الموافقة على طلب التفعيل:', error);
            return {
                success: false,
                message: 'حدث خطأ في النظام'
            };
        }
    }

    /**
     * رفض طلب التفعيل
     */
    async rejectActivationRequest(requestId, reason) {
        try {
            await this.db.run(
                `UPDATE activation_requests 
                 SET status = ?, rejection_reason = ?
                 WHERE id = ?`,
                ['rejected', reason, requestId]
            );

            // إزالة من الذاكرة
            this.activationRequests.delete(requestId);

            // تسجيل الحدث
            await this.logEvent('activation_request_rejected', {
                requestId: requestId,
                reason: reason
            });

            console.log(`❌ تم رفض طلب التفعيل: ${requestId}`);

            return {
                success: true,
                message: 'تم رفض طلب التفعيل'
            };

        } catch (error) {
            console.error('خطأ في رفض طلب التفعيل:', error);
            return {
                success: false,
                message: 'حدث خطأ في النظام'
            };
        }
    }

    /**
     * إنشاء ترخيص جديد
     */
    async createLicense(licenseData) {
        try {
            const licenseId = this.generateLicenseId();
            const licenseKey = this.generateLicenseKey();

            await this.db.run(
                `INSERT INTO licenses 
                 (id, license_key, client_name, client_email, client_phone, 
                  company_name, device_fingerprint, features, expiry_date, 
                  max_users, status, notes)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    licenseId,
                    licenseKey,
                    licenseData.clientName,
                    licenseData.clientEmail || null,
                    licenseData.clientPhone || null,
                    licenseData.companyName || null,
                    licenseData.deviceFingerprint || null,
                    JSON.stringify(licenseData.features || {}),
                    licenseData.expiryDate || null,
                    licenseData.maxUsers || 1,
                    'active',
                    licenseData.notes || null
                ]
            );

            // تسجيل الحدث
            await this.logEvent('license_created', {
                licenseId: licenseId,
                licenseKey: licenseKey,
                clientName: licenseData.clientName
            });

            console.log(`🔑 تم إنشاء ترخيص جديد: ${licenseKey}`);

            return {
                success: true,
                message: 'تم إنشاء الترخيص بنجاح',
                licenseKey: licenseKey,
                licenseId: licenseId
            };

        } catch (error) {
            console.error('خطأ في إنشاء الترخيص:', error);
            return {
                success: false,
                message: 'حدث خطأ في النظام'
            };
        }
    }

    /**
     * الحصول على جميع التراخيص
     */
    async getAllLicenses() {
        try {
            return await this.db.all('SELECT * FROM licenses ORDER BY created_at DESC');
        } catch (error) {
            console.error('خطأ في الحصول على التراخيص:', error);
            return [];
        }
    }

    /**
     * تحديث ترخيص
     */
    async updateLicense(licenseId, updateData) {
        try {
            const setClause = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
            const values = [...Object.values(updateData), licenseId];

            await this.db.run(
                `UPDATE licenses SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
                values
            );

            // تسجيل الحدث
            await this.logEvent('license_updated', {
                licenseId: licenseId,
                updateData: updateData
            });

            return {
                success: true,
                message: 'تم تحديث الترخيص بنجاح'
            };

        } catch (error) {
            console.error('خطأ في تحديث الترخيص:', error);
            return {
                success: false,
                message: 'حدث خطأ في النظام'
            };
        }
    }

    /**
     * حذف ترخيص
     */
    async deleteLicense(licenseId) {
        try {
            await this.db.run('DELETE FROM licenses WHERE id = ?', [licenseId]);

            // تسجيل الحدث
            await this.logEvent('license_deleted', {
                licenseId: licenseId
            });

            return {
                success: true,
                message: 'تم حذف الترخيص بنجاح'
            };

        } catch (error) {
            console.error('خطأ في حذف الترخيص:', error);
            return {
                success: false,
                message: 'حدث خطأ في النظام'
            };
        }
    }

    /**
     * تسجيل حدث
     */
    async logEvent(eventType, eventData, userId = null, ipAddress = null) {
        try {
            await this.db.run(
                `INSERT INTO event_log (event_type, event_data, user_id, ip_address)
                 VALUES (?, ?, ?, ?)`,
                [eventType, JSON.stringify(eventData), userId, ipAddress]
            );
        } catch (error) {
            console.error('خطأ في تسجيل الحدث:', error);
        }
    }

    /**
     * توليد مفتاح التشفير
     */
    generateEncryptionKey() {
        return crypto.randomBytes(32).toString('hex');
    }

    /**
     * توليد معرف طلب التفعيل
     */
    generateRequestId() {
        return 'REQ-' + crypto.randomBytes(8).toString('hex').toUpperCase();
    }

    /**
     * توليد معرف الترخيص
     */
    generateLicenseId() {
        return 'LIC-' + crypto.randomBytes(8).toString('hex').toUpperCase();
    }

    /**
     * توليد مفتاح الترخيص
     */
    generateLicenseKey() {
        const prefix = 'FL';
        const segments = [];
        
        for (let i = 0; i < 4; i++) {
            segments.push(crypto.randomBytes(2).toString('hex').toUpperCase());
        }
        
        return `${prefix}-${segments.join('-')}`;
    }

    /**
     * توليد معرف الجلسة
     */
    generateSessionId() {
        return 'SES-' + crypto.randomBytes(16).toString('hex').toUpperCase();
    }

    /**
     * الحصول على رسالة الحالة
     */
    getStatusMessage(status) {
        const messages = {
            'pending': 'قيد المراجعة',
            'approved': 'تم الموافقة',
            'rejected': 'تم الرفض'
        };
        return messages[status] || 'حالة غير معروفة';
    }
}

module.exports = LicenseManager;
