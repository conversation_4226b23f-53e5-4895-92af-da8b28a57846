// نظام تسجيل الدخول والمصادقة
class LoginSystem {
    constructor() {
        this.isAuthenticated = false;
        this.currentUser = null;
        this.licenseStatus = 'inactive';
        this.init();
    }

    init() {
        console.log('🔐 تهيئة نظام تسجيل الدخول...');
        this.setupEventListeners();
        this.updateSystemTime();
        this.checkConnectionStatus();
        this.loadStatesData();
        this.checkLicenseStatus();
        
        // تحديث الوقت كل ثانية
        setInterval(() => this.updateSystemTime(), 1000);
        
        // فحص الاتصال كل 30 ثانية
        setInterval(() => this.checkConnectionStatus(), 30000);
    }

    setupEventListeners() {
        // نموذج تسجيل الدخول
        const loginForm = document.getElementById('login-form');
        const togglePassword = document.getElementById('toggle-password');
        const activationBtn = document.getElementById('activation-btn');
        const contactBtn = document.getElementById('contact-btn');

        // النوافذ المنبثقة
        const activationModal = document.getElementById('activation-modal');
        const contactModal = document.getElementById('contact-modal');
        const closeActivationModal = document.getElementById('close-activation-modal');
        const closeContactModal = document.getElementById('close-contact-modal');
        const cancelActivation = document.getElementById('cancel-activation');

        // نموذج التفعيل
        const activationForm = document.getElementById('activation-form');
        const stateSelect = document.getElementById('state');
        const municipalitySelect = document.getElementById('municipality');

        // أحداث تسجيل الدخول
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        if (togglePassword) {
            togglePassword.addEventListener('click', () => this.togglePasswordVisibility());
        }

        // أحداث النوافذ المنبثقة
        if (activationBtn) {
            activationBtn.addEventListener('click', () => this.showActivationModal());
        }

        if (contactBtn) {
            contactBtn.addEventListener('click', () => this.showContactModal());
        }

        if (closeActivationModal) {
            closeActivationModal.addEventListener('click', () => this.hideActivationModal());
        }

        if (closeContactModal) {
            closeContactModal.addEventListener('click', () => this.hideContactModal());
        }

        if (cancelActivation) {
            cancelActivation.addEventListener('click', () => this.hideActivationModal());
        }

        // إغلاق النوافذ بالنقر خارجها
        if (activationModal) {
            activationModal.addEventListener('click', (e) => {
                if (e.target === activationModal) this.hideActivationModal();
            });
        }

        if (contactModal) {
            contactModal.addEventListener('click', (e) => {
                if (e.target === contactModal) this.hideContactModal();
            });
        }

        // نموذج التفعيل
        if (activationForm) {
            activationForm.addEventListener('submit', (e) => this.handleActivationRequest(e));
        }

        if (stateSelect) {
            stateSelect.addEventListener('change', () => this.updateMunicipalities());
        }

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideAllModals();
            }
        });
    }

    async handleLogin(event) {
        event.preventDefault();
        
        const loginBtn = document.getElementById('login-btn');
        const spinner = document.getElementById('login-spinner');
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember-me').checked;

        // تعطيل الزر وإظهار التحميل
        loginBtn.disabled = true;
        spinner.style.display = 'block';

        try {
            console.log('🔑 محاولة تسجيل الدخول...');

            // التحقق من بيانات الاعتماد باستخدام IPC
            const authResult = await this.authenticateUser(username, password);

            if (!authResult.success) {
                this.showError(authResult.error || 'فشل في تسجيل الدخول');
                return;
            }

            // فحص حالة الترخيص
            const licenseValid = await this.validateLicense();

            if (!licenseValid) {
                this.showError('الترخيص غير صالح أو منتهي الصلاحية. يرجى طلب تفعيل جديد.');
                return;
            }

            // حفظ بيانات المستخدم
            if (rememberMe) {
                localStorage.setItem('rememberedUser', username);
            }

            // تسجيل الدخول بنجاح
            this.isAuthenticated = true;
            this.currentUser = authResult.user;

            // حفظ الجلسة مع تفاصيل إضافية
            const sessionData = {
                ...authResult.user,
                loginTime: new Date().toISOString(),
                expiryDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 ساعة
                isValid: true
            };

            await this.saveSession(sessionData);

            this.showSuccess(`مرحباً ${authResult.user.username}! تم تسجيل الدخول بنجاح`);

            // انتقال إلى التطبيق الرئيسي
            setTimeout(() => {
                this.redirectToMainApp();
            }, 2000);

        } catch (error) {
            console.error('❌ خطأ في تسجيل الدخول:', error);
            this.showError(error.message || 'حدث خطأ أثناء تسجيل الدخول');
        } finally {
            // إعادة تفعيل الزر وإخفاء التحميل
            loginBtn.disabled = false;
            spinner.style.display = 'none';
        }
    }

    async authenticateUser(username, password) {
        try {
            if (window.electronAPI && window.electronAPI.validateCredentials) {
                // استخدام IPC للتحقق من بيانات الاعتماد
                return await window.electronAPI.validateCredentials(username, password);
            } else {
                // نظام احتياطي للتحقق المحلي
                return await this.simulateLogin(username, password);
            }
        } catch (error) {
            console.error('خطأ في المصادقة:', error);
            return { success: false, error: 'حدث خطأ أثناء التحقق من البيانات' };
        }
    }

    async simulateLogin(username, password) {
        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 1500));

        // بيانات اعتماد افتراضية للاختبار
        const validCredentials = [
            { username: 'admin', password: 'admin123', role: 'مدير' },
            { username: 'user', password: 'user123', role: 'مستخدم' },
            { username: 'manager', password: 'manager123', role: 'مشرف' }
        ];

        const user = validCredentials.find(cred =>
            cred.username === username && cred.password === password
        );

        if (!user) {
            return { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
        }

        return {
            success: true,
            user: {
                username: user.username,
                role: user.role,
                loginTime: new Date().toISOString()
            }
        };
    }

    async validateLicense() {
        // محاكاة فحص الترخيص
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // فحص الترخيص المحلي
        const localLicense = localStorage.getItem('appLicense');
        if (!localLicense) {
            return false;
        }

        try {
            const license = JSON.parse(localLicense);
            const now = new Date();
            const expiryDate = new Date(license.expiryDate);
            
            return now < expiryDate && license.status === 'active';
        } catch {
            return false;
        }
    }

    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleBtn = document.getElementById('toggle-password');
        const icon = toggleBtn.querySelector('i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }

    showActivationModal() {
        const modal = document.getElementById('activation-modal');
        modal.classList.add('show');
        modal.style.display = 'flex';
    }

    hideActivationModal() {
        const modal = document.getElementById('activation-modal');
        modal.classList.remove('show');
        modal.style.display = 'none';
    }

    showContactModal() {
        const modal = document.getElementById('contact-modal');
        modal.classList.add('show');
        modal.style.display = 'flex';
    }

    hideContactModal() {
        const modal = document.getElementById('contact-modal');
        modal.classList.remove('show');
        modal.style.display = 'none';
    }

    hideAllModals() {
        this.hideActivationModal();
        this.hideContactModal();
    }

    loadStatesData() {
        const stateSelect = document.getElementById('state');
        if (!stateSelect || !window.algeriaData) return;

        // مسح الخيارات الموجودة
        stateSelect.innerHTML = '<option value="">اختر الولاية</option>';

        // إضافة الولايات
        const states = window.algeriaData.getAllStates();
        states.forEach(state => {
            const option = document.createElement('option');
            option.value = state.code;
            option.textContent = `${state.code} - ${state.name}`;
            stateSelect.appendChild(option);
        });
    }

    updateMunicipalities() {
        const stateSelect = document.getElementById('state');
        const municipalitySelect = document.getElementById('municipality');
        
        if (!stateSelect || !municipalitySelect || !window.algeriaData) return;

        const selectedStateCode = stateSelect.value;
        
        // مسح البلديات
        municipalitySelect.innerHTML = '<option value="">اختر البلدية</option>';

        if (selectedStateCode) {
            const municipalities = window.algeriaData.getMunicipalities(selectedStateCode);
            municipalities.forEach(municipality => {
                const option = document.createElement('option');
                option.value = municipality;
                option.textContent = municipality;
                municipalitySelect.appendChild(option);
            });
        }
    }

    async handleActivationRequest(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const activationData = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            phone: formData.get('phone'),
            state: formData.get('state'),
            municipality: formData.get('municipality'),
            businessName: formData.get('businessName'),
            notes: formData.get('notes'),
            timestamp: new Date().toISOString(),
            deviceInfo: this.getDeviceInfo()
        };

        try {
            console.log('📤 إرسال طلب التفعيل...', activationData);
            
            // حفظ الطلب محلياً
            this.saveActivationRequest(activationData);
            
            // محاكاة إرسال الطلب
            await this.submitActivationRequest(activationData);
            
            this.showSuccess('تم إرسال طلب التفعيل بنجاح! سيتم التواصل معك قريباً.');
            this.hideActivationModal();
            
            // إعادة تعيين النموذج
            event.target.reset();
            
        } catch (error) {
            console.error('❌ خطأ في إرسال طلب التفعيل:', error);
            this.showError('حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
        }
    }

    async submitActivationRequest(data) {
        try {
            console.log('📤 إرسال طلب التفعيل إلى خادم التراخيص...');

            // إعداد بيانات الطلب
            const requestData = {
                ...data,
                deviceId: this.getDeviceInfo().deviceId,
                systemInfo: this.getSystemInfo(),
                timestamp: new Date().toISOString()
            };

            // محاولة إرسال الطلب إلى خادم التراخيص
            const licenseServerUrl = 'http://localhost:3000/api/activation-request';

            const response = await fetch(licenseServerUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ تم إرسال طلب التفعيل بنجاح:', result.requestId);
                return result;
            } else {
                throw new Error(`خطأ في الخادم: ${response.status}`);
            }

        } catch (error) {
            console.warn('⚠️ فشل في الاتصال بخادم التراخيص، سيتم الحفظ محلياً:', error.message);

            // في حالة فشل الاتصال، احفظ الطلب محلياً
            const fallbackResult = {
                success: true,
                requestId: this.generateRequestId(),
                message: 'تم حفظ الطلب محلياً - سيتم إرساله عند توفر الاتصال',
                offline: true
            };

            // حفظ الطلب في قائمة الانتظار للإرسال لاحقاً
            this.saveOfflineRequest(data);

            return fallbackResult;
        }
    }

    saveActivationRequest(data) {
        const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
        requests.push(data);
        localStorage.setItem('activationRequests', JSON.stringify(requests));
    }

    generateRequestId() {
        return 'REQ-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }

    saveOfflineRequest(data) {
        try {
            const offlineRequests = JSON.parse(localStorage.getItem('offlineActivationRequests') || '[]');
            const requestWithId = {
                ...data,
                id: this.generateRequestId(),
                timestamp: new Date().toISOString(),
                status: 'pending_upload'
            };

            offlineRequests.push(requestWithId);
            localStorage.setItem('offlineActivationRequests', JSON.stringify(offlineRequests));

            console.log('💾 تم حفظ طلب التفعيل محلياً للإرسال لاحقاً');
        } catch (error) {
            console.error('❌ خطأ في حفظ الطلب محلياً:', error);
        }
    }

    getSystemInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            screenResolution: `${screen.width}x${screen.height}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timestamp: new Date().toISOString()
        };
    }

    // محاولة إرسال الطلبات المحفوظة محلياً
    async retryOfflineRequests() {
        try {
            const offlineRequests = JSON.parse(localStorage.getItem('offlineActivationRequests') || '[]');

            if (offlineRequests.length === 0) {
                return;
            }

            console.log(`🔄 محاولة إرسال ${offlineRequests.length} طلب محفوظ محلياً...`);

            const successfulRequests = [];

            for (const request of offlineRequests) {
                try {
                    const result = await this.submitActivationRequest(request);
                    if (result.success && !result.offline) {
                        successfulRequests.push(request.id);
                        console.log(`✅ تم إرسال الطلب ${request.id} بنجاح`);
                    }
                } catch (error) {
                    console.warn(`⚠️ فشل في إرسال الطلب ${request.id}:`, error.message);
                }
            }

            // إزالة الطلبات التي تم إرسالها بنجاح
            if (successfulRequests.length > 0) {
                const remainingRequests = offlineRequests.filter(req => !successfulRequests.includes(req.id));
                localStorage.setItem('offlineActivationRequests', JSON.stringify(remainingRequests));

                console.log(`✅ تم إرسال ${successfulRequests.length} طلب بنجاح`);
            }

        } catch (error) {
            console.error('❌ خطأ في محاولة إرسال الطلبات المحفوظة:', error);
        }
    }

    getDeviceInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            screenResolution: `${screen.width}x${screen.height}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        };
    }

    updateSystemTime() {
        const timeElement = document.getElementById('system-time');
        if (timeElement) {
            const now = new Date();
            const timeString = now.toLocaleString('ar-DZ', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            timeElement.textContent = timeString;
        }
    }

    async checkConnectionStatus() {
        const statusElement = document.getElementById('connection-status');
        if (!statusElement) return;

        try {
            // محاكاة فحص الاتصال
            const response = await fetch('https://www.google.com/favicon.ico', {
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache'
            });
            
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> متصل';
            statusElement.className = 'connection-status connected';
        } catch {
            statusElement.innerHTML = '<i class="fas fa-wifi-slash"></i> غير متصل';
            statusElement.className = 'connection-status disconnected';
        }
    }

    checkLicenseStatus() {
        const statusElement = document.getElementById('license-status');
        if (!statusElement) return;

        const license = localStorage.getItem('appLicense');
        if (license) {
            try {
                const licenseData = JSON.parse(license);
                const now = new Date();
                const expiryDate = new Date(licenseData.expiryDate);
                
                if (now < expiryDate && licenseData.status === 'active') {
                    statusElement.innerHTML = `
                        <div class="status-indicator">
                            <i class="fas fa-check-circle"></i>
                            <span>الترخيص نشط - ينتهي في ${this.formatDate(expiryDate)}</span>
                        </div>
                    `;
                    statusElement.style.background = 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)';
                    this.licenseStatus = 'active';
                } else {
                    this.showExpiredLicense();
                }
            } catch {
                this.showInvalidLicense();
            }
        } else {
            this.showNoLicense();
        }
    }

    showExpiredLicense() {
        const statusElement = document.getElementById('license-status');
        statusElement.innerHTML = `
            <div class="status-indicator">
                <i class="fas fa-exclamation-triangle"></i>
                <span>الترخيص منتهي الصلاحية - يرجى التجديد</span>
            </div>
        `;
        statusElement.style.background = 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)';
        this.licenseStatus = 'expired';
    }

    showInvalidLicense() {
        const statusElement = document.getElementById('license-status');
        statusElement.innerHTML = `
            <div class="status-indicator">
                <i class="fas fa-times-circle"></i>
                <span>ترخيص غير صالح - يرجى طلب ترخيص جديد</span>
            </div>
        `;
        statusElement.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
        this.licenseStatus = 'invalid';
    }

    showNoLicense() {
        const statusElement = document.getElementById('license-status');
        statusElement.innerHTML = `
            <div class="status-indicator">
                <i class="fas fa-exclamation-triangle"></i>
                <span>يتطلب تفعيل الترخيص</span>
            </div>
        `;
        statusElement.style.background = 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)';
        this.licenseStatus = 'inactive';
    }

    formatDate(date) {
        return date.toLocaleDateString('ar-DZ', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showToast(message, type = 'info') {
        // إنشاء عنصر التنبيه
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        // إضافة الأنماط
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#2ecc71' : type === 'error' ? '#e74c3c' : '#3498db'};
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
            max-width: 400px;
            word-wrap: break-word;
        `;

        document.body.appendChild(toast);

        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 5000);
    }

    async redirectToMainApp() {
        try {
            console.log('🔄 إعادة توجيه إلى التطبيق الرئيسي...');

            // التأكد من حفظ بيانات الجلسة (تم حفظها مسبقاً في handleLogin)
            console.log('✅ بيانات الجلسة محفوظة مسبقاً');

            // إعادة تحميل النافذة مع التطبيق الرئيسي
            if (window.electronAPI && window.electronAPI.reloadMainApp) {
                await window.electronAPI.reloadMainApp();
            } else {
                // في حالة عدم وجود Electron API، استخدم إعادة التوجيه العادي
                window.location.href = '../../index.html';
            }
        } catch (error) {
            console.error('❌ خطأ في إعادة التوجيه:', error);
            this.showError('حدث خطأ أثناء تحميل التطبيق');
        }
    }

    async saveSession(sessionData = null) {
        try {
            // إذا لم يتم تمرير بيانات، استخدم البيانات الحالية
            const dataToSave = sessionData || {
                username: this.currentUser.username,
                role: this.currentUser.role,
                loginTime: this.currentUser.loginTime,
                expiryDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 ساعة
                isValid: true
            };

            if (window.electronAPI && window.electronAPI.saveSession) {
                await window.electronAPI.saveSession(dataToSave);
            } else {
                // حفظ في localStorage كبديل
                localStorage.setItem('userSession', JSON.stringify(dataToSave));
            }

            console.log('✅ تم حفظ بيانات الجلسة:', dataToSave.username);
        } catch (error) {
            console.error('❌ خطأ في حفظ الجلسة:', error);
        }
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    window.loginSystem = new LoginSystem();

    // محاولة إرسال الطلبات المحفوظة محلياً
    setTimeout(() => {
        window.loginSystem.retryOfflineRequests();
    }, 2000); // انتظار ثانيتين للتأكد من تحميل النظام
});

// إضافة أنماط CSS للتنبيهات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .toast-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .toast-content i {
        font-size: 1.2rem;
    }
`;
document.head.appendChild(style);
