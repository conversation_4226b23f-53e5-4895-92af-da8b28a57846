console.log('بدء تشغيل الخادم...');

const http = require('http');

const server = http.createServer((req, res) => {
    console.log('طلب جديد:', req.method, req.url);
    
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ message: 'الخادم يعمل!' }));
});

server.listen(3000, () => {
    console.log('الخادم يعمل على المنفذ 3000');
});
